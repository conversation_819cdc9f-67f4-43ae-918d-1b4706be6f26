<?php
/**
 * Debug ACF Fields
 */

// Laad WordPress
require_once('wp-load.php');

// Controleer of de gebruiker is ingelogd
if (!is_user_logged_in() || !current_user_can('administrator')) {
    wp_die('Je hebt geen toegang tot deze pagina.');
}

// Haal alle ACF veldgroepen op
$field_groups = acf_get_field_groups();

// Toon de veldgroepen
echo '<h1>ACF Veldgroepen</h1>';
echo '<pre>';
foreach ($field_groups as $field_group) {
    echo "Veldgroep: {$field_group['title']} (key: {$field_group['key']})\n";
    
    // Haal alle velden op voor deze veldgroep
    $fields = acf_get_fields($field_group['key']);
    
    // Toon de velden
    foreach ($fields as $field) {
        echo "  - Veld: {$field['label']} (name: {$field['name']}, key: {$field['key']})\n";
        
        // Als het een repeater veld is, toon de subvelden
        if ($field['type'] === 'repeater' && isset($field['sub_fields'])) {
            foreach ($field['sub_fields'] as $sub_field) {
                echo "    - Subveld: {$sub_field['label']} (name: {$sub_field['name']}, key: {$sub_field['key']})\n";
            }
        }
    }
    
    echo "\n";
}
echo '</pre>';
