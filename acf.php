<?php
/**
 * Centralized ACF Management System
 * 
 * This file manages all ACF blocks, fields, and post types from code.
 * All ACF configurations are centralized here for better maintainability.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ACF Management Class
 */
class ACF_Manager {
    
    public function __construct() {
        add_action('init', array($this, 'register_post_types'));
        add_action('acf/init', array($this, 'register_field_groups'));
        add_action('acf/init', array($this, 'register_options_pages'));
        add_filter('block_categories_all', array($this, 'register_block_categories'), 10, 2);

        // Restrict blocks to only ACF blocks
        add_filter('allowed_block_types_all', array($this, 'restrict_to_acf_blocks'), 10, 2);

        // Add redirect for rooms archive to rooms page
        add_action('template_redirect', array($this, 'redirect_rooms_archive'));

        // Include blocks configuration
        $this->include_blocks_config();
    }

    /**
     * Include blocks configuration file
     */
    private function include_blocks_config() {
        require_once get_template_directory() . '/acf-blocks.php';
    }
    
    /**
     * Register block categories
     */
    public function register_block_categories($categories, $post) {
        return array_merge($categories, array(
            array(
                'slug'  => 'rooms',
                'title' => __('Rooms', 'textdomain'),
                'icon'  => 'building',
            ),
        ));
    }

    /**
     * Restrict blocks to only ACF blocks
     */
    public function restrict_to_acf_blocks($allowed_blocks, $editor_context) {
        // Get all registered ACF blocks
        $acf_blocks = array();

        // Get all registered blocks
        $registered_blocks = WP_Block_Type_Registry::get_instance()->get_all_registered();

        // Filter to only ACF blocks (blocks that start with 'acf/')
        foreach ($registered_blocks as $block_name => $block_type) {
            if (strpos($block_name, 'acf/') === 0) {
                $acf_blocks[] = $block_name;
            }
        }

        return $acf_blocks;
    }

    /**
     * Redirect rooms archive to rooms page
     */
    public function redirect_rooms_archive() {
        // Check if we're on the rooms archive page
        if (is_post_type_archive('rooms')) {
            // Get the rooms page by slug
            $rooms_page = get_page_by_path('rooms');

            if ($rooms_page) {
                // Redirect to the rooms page
                wp_redirect(get_permalink($rooms_page->ID), 301);
                exit;
            }
        }
    }

    /**
     * Register all custom post types
     */
    public function register_post_types() {
        $this->register_rooms_post_type();
        $this->register_popup_post_type();
        $this->register_partner_post_type();
    }
    
    /**
     * Register Rooms post type
     */
    private function register_rooms_post_type() {
        $labels = array(
            'name'                  => _x('Rooms', 'Post Type General Name', 'textdomain'),
            'singular_name'         => _x('Room', 'Post Type Singular Name', 'textdomain'),
            'menu_name'             => __('Rooms', 'textdomain'),
            'name_admin_bar'        => __('Room', 'textdomain'),
            'archives'              => __('Room Archives', 'textdomain'),
            'attributes'            => __('Room Attributes', 'textdomain'),
            'parent_item_colon'     => __('Parent Room:', 'textdomain'),
            'all_items'             => __('All Rooms', 'textdomain'),
            'add_new_item'          => __('Add New Room', 'textdomain'),
            'add_new'               => __('Add New', 'textdomain'),
            'new_item'              => __('New Room', 'textdomain'),
            'edit_item'             => __('Edit Room', 'textdomain'),
            'update_item'           => __('Update Room', 'textdomain'),
            'view_item'             => __('View Room', 'textdomain'),
            'view_items'            => __('View Rooms', 'textdomain'),
            'search_items'          => __('Search Room', 'textdomain'),
            'not_found'             => __('Not found', 'textdomain'),
            'not_found_in_trash'    => __('Not found in Trash', 'textdomain'),
            'featured_image'        => __('Featured Image', 'textdomain'),
            'set_featured_image'    => __('Set featured image', 'textdomain'),
            'remove_featured_image' => __('Remove featured image', 'textdomain'),
            'use_featured_image'    => __('Use as featured image', 'textdomain'),
            'insert_into_item'      => __('Insert into room', 'textdomain'),
            'uploaded_to_this_item' => __('Uploaded to this room', 'textdomain'),
            'items_list'            => __('Rooms list', 'textdomain'),
            'items_list_navigation' => __('Rooms list navigation', 'textdomain'),
            'filter_items_list'     => __('Filter rooms list', 'textdomain'),
        );
        
        $args = array(
            'label'                 => __('Room', 'textdomain'),
            'description'           => __('Room items', 'textdomain'),
            'labels'                => $labels,
            'supports'              => array('title', 'editor', 'thumbnail', 'excerpt'),
            'hierarchical'          => false,
            'public'                => true,
            'show_ui'               => true,
            'show_in_menu'          => true,
            'menu_position'         => 20,
            'menu_icon'             => 'dashicons-building',
            'show_in_admin_bar'     => true,
            'show_in_nav_menus'     => true,
            'can_export'            => true,
            'has_archive'           => false,
            'exclude_from_search'   => false,
            'publicly_queryable'    => true,
            'capability_type'       => 'post',
            'show_in_rest'          => true,
            'rewrite'               => array('slug' => 'room'),
        );
        
        register_post_type('rooms', $args);

        // Flush rewrite rules if this is the first time registering
        if (get_option('rooms_post_type_flushed') !== 'yes') {
            flush_rewrite_rules();
            update_option('rooms_post_type_flushed', 'yes');
        }
    }
    
    /**
     * Register Partner post type
     */
    private function register_partner_post_type() {
        $labels = array(
            'name'                  => _x('Partners', 'Post Type General Name', 'textdomain'),
            'singular_name'         => _x('Partner', 'Post Type Singular Name', 'textdomain'),
            'menu_name'             => __('Partners', 'textdomain'),
            'name_admin_bar'        => __('Partner', 'textdomain'),
            'archives'              => __('Partner Archives', 'textdomain'),
            'attributes'            => __('Partner Attributes', 'textdomain'),
            'parent_item_colon'     => __('Parent Partner:', 'textdomain'),
            'all_items'             => __('All Partners', 'textdomain'),
            'add_new_item'          => __('Add New Partner', 'textdomain'),
            'add_new'               => __('Add New', 'textdomain'),
            'new_item'              => __('New Partner', 'textdomain'),
            'edit_item'             => __('Edit Partner', 'textdomain'),
            'update_item'           => __('Update Partner', 'textdomain'),
            'view_item'             => __('View Partner', 'textdomain'),
            'view_items'            => __('View Partners', 'textdomain'),
            'search_items'          => __('Search Partner', 'textdomain'),
            'not_found'             => __('Not found', 'textdomain'),
            'not_found_in_trash'    => __('Not found in Trash', 'textdomain'),
            'featured_image'        => __('Featured Image', 'textdomain'),
            'set_featured_image'    => __('Set featured image', 'textdomain'),
            'remove_featured_image' => __('Remove featured image', 'textdomain'),
            'use_featured_image'    => __('Use as featured image', 'textdomain'),
            'insert_into_item'      => __('Insert into partner', 'textdomain'),
            'uploaded_to_this_item' => __('Uploaded to this partner', 'textdomain'),
            'items_list'            => __('Partners list', 'textdomain'),
            'items_list_navigation' => __('Partners list navigation', 'textdomain'),
            'filter_items_list'     => __('Filter partners list', 'textdomain'),
        );
        
        $args = array(
            'label'                 => __('Partner', 'textdomain'),
            'description'           => __('Partner items', 'textdomain'),
            'labels'                => $labels,
            'supports'              => array('title', 'thumbnail'),
            'hierarchical'          => false,
            'public'                => true,
            'show_ui'               => true,
            'show_in_menu'          => true,
            'menu_position'         => 22,
            'menu_icon'             => 'dashicons-groups',
            'show_in_admin_bar'     => true,
            'show_in_nav_menus'     => true,
            'can_export'            => true,
            'has_archive'           => true,
            'exclude_from_search'   => false,
            'publicly_queryable'    => true,
            'capability_type'       => 'post',
            'show_in_rest'          => true,
            'rewrite'               => array('slug' => 'partners'),
        );
        
        register_post_type('partner', $args);
    }


    /**
     * Register Popup post type (existing)
     */
    private function register_popup_post_type() {
        $labels = array(
            'name'                  => _x('Popups', 'Post Type General Name', 'textdomain'),
            'singular_name'         => _x('Popup', 'Post Type Singular Name', 'textdomain'),
            'menu_name'             => __('Popups', 'textdomain'),
            'name_admin_bar'        => __('Popup', 'textdomain'),
            'archives'              => __('Popup Archives', 'textdomain'),
            'attributes'            => __('Popup Attributes', 'textdomain'),
            'parent_item_colon'     => __('Parent Popup:', 'textdomain'),
            'all_items'             => __('All Popups', 'textdomain'),
            'add_new_item'          => __('Add New Popup', 'textdomain'),
            'add_new'               => __('Add New', 'textdomain'),
            'new_item'              => __('New Popup', 'textdomain'),
            'edit_item'             => __('Edit Popup', 'textdomain'),
            'update_item'           => __('Update Popup', 'textdomain'),
            'view_item'             => __('View Popup', 'textdomain'),
            'view_items'            => __('View Popups', 'textdomain'),
            'search_items'          => __('Search Popup', 'textdomain'),
            'not_found'             => __('Not found', 'textdomain'),
            'not_found_in_trash'    => __('Not found in Trash', 'textdomain'),
            'featured_image'        => __('Featured Image', 'textdomain'),
            'set_featured_image'    => __('Set featured image', 'textdomain'),
            'remove_featured_image' => __('Remove featured image', 'textdomain'),
            'use_featured_image'    => __('Use as featured image', 'textdomain'),
            'insert_into_item'      => __('Insert into popup', 'textdomain'),
            'uploaded_to_this_item' => __('Uploaded to this popup', 'textdomain'),
            'items_list'            => __('Popups list', 'textdomain'),
            'items_list_navigation' => __('Popups list navigation', 'textdomain'),
            'filter_items_list'     => __('Filter popups list', 'textdomain'),
        );
        
        $args = array(
            'label'                 => __('Popup', 'textdomain'),
            'description'           => __('Popup items', 'textdomain'),
            'labels'                => $labels,
            'supports'              => array('title', 'editor', 'thumbnail'),
            'hierarchical'          => false,
            'public'                => true,
            'show_ui'               => true,
            'show_in_menu'          => true,
            'menu_position'         => 21,
            'menu_icon'             => 'dashicons-megaphone',
            'show_in_admin_bar'     => true,
            'show_in_nav_menus'     => false,
            'can_export'            => true,
            'has_archive'           => false,
            'exclude_from_search'   => true,
            'publicly_queryable'    => false,
            'capability_type'       => 'page',
            'show_in_rest'          => true,
        );
        
        register_post_type('popup', $args);
    }
    
    /**
     * Register ACF Options Pages
     */
    public function register_options_pages() {
        if (function_exists('acf_add_options_page')) {
            acf_add_options_page(array(
                'page_title' => 'Theme General Settings',
                'menu_title' => 'Theme Settings',
                'menu_slug' => 'theme-general-settings',
                'capability' => 'edit_posts',
                'redirect' => false
            ));

            // Add Menus options page
            acf_add_options_page(array(
                'page_title' => 'Menus Management',
                'menu_title' => 'Menus',
                'menu_slug' => 'menus-settings',
                'capability' => 'edit_posts',
                'redirect' => false,
                'icon_url' => 'dashicons-media-document',
                'position' => 25
            ));
        }
    }
    






    /**
     * Register all ACF field groups
     */
    public function register_field_groups() {
        if (!function_exists('acf_add_local_field_group')) {
            return;
        }

        $this->register_rooms_fields();
        $this->register_popup_fields();
        $this->register_opening_hours_fields();
        $this->register_partner_fields();
        $this->register_menus_fields();
    }

    /**
     * Register Rooms field group
     */
    private function register_rooms_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_rooms_fields',
            'title' => 'Room Details',
            'fields' => array(
                array(
                    'key' => 'field_room_color',
                    'label' => 'Room Color',
                    'name' => 'room_color',
                    'type' => 'color_picker',
                    'instructions' => 'Choose a color that represents this room',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '50',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '#3498db',
                    'enable_opacity' => 0,
                    'return_format' => 'string',
                ),
                array(
                    'key' => 'field_room_title',
                    'label' => 'Room Title',
                    'name' => 'room_title',
                    'type' => 'text',
                    'instructions' => 'Enter the main title for this room',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '50',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'e.g., Deluxe Suite',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 100,
                ),
                array(
                    'key' => 'field_room_description',
                    'label' => 'Room Description',
                    'name' => 'room_description',
                    'type' => 'wysiwyg',
                    'instructions' => 'Provide a detailed description of the room',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'tabs' => 'all',
                    'toolbar' => 'full',
                    'media_upload' => 1,
                    'delay' => 0,
                ),
                array(
                    'key' => 'field_room_gallery',
                    'label' => 'Room Gallery',
                    'name' => 'room_gallery',
                    'type' => 'gallery',
                    'instructions' => 'Upload images to showcase this room',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'insert' => 'append',
                    'library' => 'all',
                    'min' => 1,
                    'max' => 20,
                    'mime_types' => 'jpg,jpeg,png,webp',
                ),
            //     array(
            //         'key' => 'field_room_amenities',
            //         'label' => 'Room Amenities',
            //         'name' => 'room_amenities',
            //         'type' => 'repeater',
            //         'instructions' => 'Add amenities available in this room',
            //         'required' => 0,
            //         'conditional_logic' => 0,
            //         'wrapper' => array(
            //             'width' => '',
            //             'class' => '',
            //             'id' => '',
            //         ),
            //         'collapsed' => 'field_amenity_name',
            //         'min' => 0,
            //         'max' => 20,
            //         'layout' => 'table',
            //         'button_label' => 'Add Amenity',
            //         'sub_fields' => array(
            //             array(
            //                 'key' => 'field_amenity_name',
            //                 'label' => 'Amenity Name',
            //                 'name' => 'amenity_name',
            //                 'type' => 'text',
            //                 'instructions' => '',
            //                 'required' => 1,
            //                 'conditional_logic' => 0,
            //                 'wrapper' => array(
            //                     'width' => '70',
            //                     'class' => '',
            //                     'id' => '',
            //                 ),
            //                 'default_value' => '',
            //                 'placeholder' => 'e.g., Free WiFi',
            //                 'prepend' => '',
            //                 'append' => '',
            //                 'maxlength' => 50,
            //             ),
            //             array(
            //                 'key' => 'field_amenity_icon',
            //                 'label' => 'Amenity Icon',
            //                 'name' => 'amenity_icon',
            //                 'type' => 'image',
            //                 'instructions' => '',
            //                 'required' => 0,
            //                 'conditional_logic' => 0,
            //                 'wrapper' => array(
            //                     'width' => '30',
            //                     'class' => '',
            //                     'id' => '',
            //                 ),
            //                 'return_format' => 'array',
            //                 'preview_size' => 'thumbnail',
            //                 'library' => 'all',
            //                 'min_width' => '',
            //                 'min_height' => '',
            //                 'min_size' => '',
            //                 'max_width' => 100,
            //                 'max_height' => 100,
            //                 'max_size' => '',
            //                 'mime_types' => 'jpg,jpeg,png,svg,webp',
            //             ),
            //         ),
            //     ),
            //     array(
            //         'key' => 'field_room_price',
            //         'label' => 'Room Price',
            //         'name' => 'room_price',
            //         'type' => 'number',
            //         'instructions' => 'Enter the price per night',
            //         'required' => 0,
            //         'conditional_logic' => 0,
            //         'wrapper' => array(
            //             'width' => '33',
            //             'class' => '',
            //             'id' => '',
            //         ),
            //         'default_value' => '',
            //         'placeholder' => '0.00',
            //         'prepend' => '€',
            //         'append' => 'per night',
            //         'min' => 0,
            //         'max' => '',
            //         'step' => 0.01,
            //     ),
            //     array(
            //         'key' => 'field_room_capacity',
            //         'label' => 'Room Capacity',
            //         'name' => 'room_capacity',
            //         'type' => 'number',
            //         'instructions' => 'Maximum number of guests',
            //         'required' => 0,
            //         'conditional_logic' => 0,
            //         'wrapper' => array(
            //             'width' => '33',
            //             'class' => '',
            //             'id' => '',
            //         ),
            //         'default_value' => 2,
            //         'placeholder' => '',
            //         'prepend' => '',
            //         'append' => 'guests',
            //         'min' => 1,
            //         'max' => 20,
            //         'step' => 1,
            //     ),
            //     array(
            //         'key' => 'field_room_size',
            //         'label' => 'Room Size',
            //         'name' => 'room_size',
            //         'type' => 'number',
            //         'instructions' => 'Room size in square meters',
            //         'required' => 0,
            //         'conditional_logic' => 0,
            //         'wrapper' => array(
            //             'width' => '34',
            //             'class' => '',
            //             'id' => '',
            //         ),
            //         'default_value' => '',
            //         'placeholder' => '',
            //         'prepend' => '',
            //         'append' => 'm²',
            //         'min' => 0,
            //         'max' => '',
            //         'step' => 1,
            //     ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'rooms',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Fields for managing room details and information',
        ));
    }



    /**
     * Register Popup field group (existing)
     */
    private function register_popup_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_popup_settings',
            'title' => 'Popup Settings',
            'fields' => array(
                array(
                    'key' => 'field_popup_enabled',
                    'label' => 'Enable Popup',
                    'name' => 'popup_enabled',
                    'type' => 'true_false',
                    'instructions' => 'Enable or disable this popup',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 0,
                    'ui' => 1,
                    'ui_on_text' => 'Enabled',
                    'ui_off_text' => 'Disabled',
                ),
                array(
                    'key' => 'field_popup_media_type',
                    'label' => 'Media Type',
                    'name' => 'popup_media_type',
                    'type' => 'radio',
                    'instructions' => 'Select the type of media to display in the popup',
                    'required' => 0,
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_popup_enabled',
                                'operator' => '==',
                                'value' => '1',
                            ),
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'choices' => array(
                        'image' => 'Image',
                        'video' => 'Video',
                        'none' => 'No Media',
                    ),
                    'allow_null' => 0,
                    'other_choice' => 0,
                    'default_value' => 'image',
                    'layout' => 'horizontal',
                    'return_format' => 'value',
                    'save_other_choice' => 0,
                ),
                array(
                    'key' => 'field_popup_image',
                    'label' => 'Popup Image',
                    'name' => 'popup_image',
                    'type' => 'image',
                    'instructions' => 'Upload an image for the popup',
                    'required' => 0,
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_popup_media_type',
                                'operator' => '==',
                                'value' => 'image',
                            ),
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
                array(
                    'key' => 'field_popup_video_type',
                    'label' => 'Video Type',
                    'name' => 'popup_video_type',
                    'type' => 'radio',
                    'instructions' => 'Select the type of video source',
                    'required' => 0,
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_popup_media_type',
                                'operator' => '==',
                                'value' => 'video',
                            ),
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'choices' => array(
                        'file' => 'Upload File',
                        'url' => 'External URL',
                    ),
                    'allow_null' => 0,
                    'other_choice' => 0,
                    'default_value' => 'file',
                    'layout' => 'horizontal',
                    'return_format' => 'value',
                    'save_other_choice' => 0,
                ),
                array(
                    'key' => 'field_popup_video',
                    'label' => 'Popup Video File',
                    'name' => 'popup_video',
                    'type' => 'file',
                    'instructions' => 'Upload a video file for the popup',
                    'required' => 0,
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_popup_media_type',
                                'operator' => '==',
                                'value' => 'video',
                            ),
                            array(
                                'field' => 'field_popup_video_type',
                                'operator' => '==',
                                'value' => 'file',
                            ),
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'library' => 'all',
                    'min_size' => '',
                    'max_size' => '',
                    'mime_types' => 'mp4,webm',
                ),
                array(
                    'key' => 'field_popup_video_url',
                    'label' => 'Popup Video URL',
                    'name' => 'popup_video_url',
                    'type' => 'url',
                    'instructions' => 'Enter the URL for an external video (YouTube, Vimeo, or direct MP4 link)',
                    'required' => 0,
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_popup_media_type',
                                'operator' => '==',
                                'value' => 'video',
                            ),
                            array(
                                'field' => 'field_popup_video_type',
                                'operator' => '==',
                                'value' => 'url',
                            ),
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'https://',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'popup',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => '',
        ));
    }

    /**
     * Register Partner field group
     */
    private function register_partner_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_partner_fields',
            'title' => 'Partner Details',
            'fields' => array(
                array(
                    'key' => 'field_partner_logo',
                    'label' => 'Partner Logo',
                    'name' => 'partner_logo',
                    'type' => 'image',
                    'instructions' => 'Upload the logo for the partner',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
                array(
                    'key' => 'field_partner_link',
                    'label' => 'Partner Link',
                    'name' => 'partner_link',
                    'type' => 'url',
                    'instructions' => 'Enter the URL for the partner\'s website',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'https://',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'post_type',
                        'operator' => '==',
                        'value' => 'partner',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => '',
        ));
    }

    /**
     * Register Opening Hours field group for Theme Settings
     */
    private function register_opening_hours_fields() {
        $days = array('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday');
        $fields = array();

        foreach ($days as $day) {
            $fields[] = $this->create_day_field($day);
        }

        acf_add_local_field_group(array(
            'key' => 'group_opening_hours',
            'title' => 'Opening Hours',
            'fields' => $fields,
            'location' => array(
                array(
                    array(
                        'param' => 'options_page',
                        'operator' => '==',
                        'value' => 'theme-general-settings',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => '',
        ));
    }

    /**
     * Create ACF field for a specific day
     *
     * @param string $day Day name (monday, tuesday, etc.)
     * @return array ACF field configuration
     */
    private function create_day_field($day) {
        $day_capitalized = ucfirst($day);

        return array(
            'key' => 'field_opening_hours_' . $day,
            'label' => $day_capitalized,
            'name' => 'opening_hours_' . $day,
            'type' => 'repeater',
            'instructions' => 'Add opening hours for ' . $day_capitalized . '. Leave empty if closed.',
            'required' => 0,
            'conditional_logic' => 0,
            'wrapper' => array(
                'width' => '',
                'class' => '',
                'id' => '',
            ),
            'collapsed' => '',
            'min' => 0,
            'max' => 3,
            'layout' => 'table',
            'button_label' => 'Add Time Slot',
            'sub_fields' => array(
                array(
                    'key' => 'field_' . $day . '_open_time',
                    'label' => 'Open Time',
                    'name' => 'open_time',
                    'type' => 'time_picker',
                    'instructions' => '',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '50',
                        'class' => '',
                        'id' => '',
                    ),
                    'display_format' => 'g:ia',
                    'return_format' => 'H:i',
                ),
                array(
                    'key' => 'field_' . $day . '_close_time',
                    'label' => 'Close Time',
                    'name' => 'close_time',
                    'type' => 'time_picker',
                    'instructions' => '',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '50',
                        'class' => '',
                        'id' => '',
                    ),
                    'display_format' => 'g:ia',
                    'return_format' => 'H:i',
                ),
            ),
        );
    }

    /**
     * Register Menus field group for Menus Options Page
     */
    private function register_menus_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_menus_settings',
            'title' => 'Menus Management',
            'fields' => array(
                array(
                    'key' => 'field_menus_list',
                    'label' => 'Menu Items',
                    'name' => 'menus_list',
                    'type' => 'repeater',
                    'instructions' => 'Add your restaurant menus here. Each menu should have a name and PDF file.',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'collapsed' => 'field_menu_name',
                    'min' => 0,
                    'max' => 0,
                    'layout' => 'table',
                    'button_label' => 'Add Menu',
                    'sub_fields' => array(
                        array(
                            'key' => 'field_menu_name',
                            'label' => 'Menu Name',
                            'name' => 'menu_name',
                            'type' => 'text',
                            'instructions' => 'Enter the name of the menu (e.g., "Lunch Menu", "Dinner Menu", "Wine List")',
                            'required' => 1,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '30',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => 'Menu Name',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => 100,
                        ),
                        array(
                            'key' => 'field_menu_category',
                            'label' => 'Category',
                            'name' => 'menu_category',
                            'type' => 'select',
                            'instructions' => 'Select the category for this menu',
                            'required' => 1,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '20',
                                'class' => '',
                                'id' => '',
                            ),
                            'choices' => array(
                                'food' => 'Food',
                                'drinks' => 'Drinks',
                                'wine' => 'Wine',
                                'dessert' => 'Dessert',
                                'special' => 'Special',
                            ),
                            'default_value' => 'food',
                            'allow_null' => 0,
                            'multiple' => 0,
                            'ui' => 1,
                            'ajax' => 0,
                            'return_format' => 'value',
                            'placeholder' => '',
                        ),
                        array(
                            'key' => 'field_menu_pdf',
                            'label' => 'Menu PDF',
                            'name' => 'menu_pdf',
                            'type' => 'file',
                            'instructions' => 'Upload the PDF file for this menu',
                            'required' => 1,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '30',
                                'class' => '',
                                'id' => '',
                            ),
                            'return_format' => 'array',
                            'library' => 'all',
                            'min_size' => '',
                            'max_size' => '10MB',
                            'mime_types' => 'pdf',
                        ),
                        array(
                            'key' => 'field_menu_description',
                            'label' => 'Description',
                            'name' => 'menu_description',
                            'type' => 'textarea',
                            'instructions' => 'Optional description for this menu',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '20',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => 'Menu description...',
                            'maxlength' => 200,
                            'rows' => 3,
                            'new_lines' => 'br',
                        ),
                    ),
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'options_page',
                        'operator' => '==',
                        'value' => 'menus-settings',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Manage your restaurant menus and PDF files',
        ));
    }
}

// Initialize the ACF Manager
new ACF_Manager();
