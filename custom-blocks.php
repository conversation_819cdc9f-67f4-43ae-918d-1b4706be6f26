<?php
function render_custom_sections() {
    // Controleer of er secties zijn.
    if( have_rows('sections') ):
        $output = '<div class="sections">';
        $last_was_black = false; // Houdt bij of de vorige sectie 'black' was.

        while( have_rows('sections') ): the_row();
            $class = get_sub_field('class'); // Verkrijg de class voor deze sectie.
            $content = get_sub_field('content'); // De inhoud van de sectie

            // Start sectie HTML
            $output .= '<div class="section ' . esc_attr($class) . '">';
            $output .= $content; // Voeg inhoud toe
            $output .= '</div>';

            // Controleer of de huidige sectie 'black' is
            if ($class === 'black') {
                if ($last_was_black) {
                    // Voeg hier extra styling toe als de vorige sectie ook 'black' was
                    $output .= '<style>
                        .section.black {
                            /* Specifieke styling voor opeenvolgende zwarte secties */
                            background-color: #333; /* Voorbeeld kleur */
                            color: #fff; /* Voorbeeld tekstkleur */
                        }
                    </style>';
                }
                $last_was_black = true; // Update de status
            } else {
                $last_was_black = false; // Reset als de sectie niet 'black' is
            }
        endwhile;

        $output .= '</div>';
        return $output; // Retourneer de HTML
    endif;

    return ''; // Retourneer een lege string als er geen secties zijn
}
