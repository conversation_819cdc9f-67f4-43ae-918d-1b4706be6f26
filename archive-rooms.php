<?php
/**
 * Archive Rooms Template
 * 
 * Template for displaying the rooms archive page
 */

get_header(); ?>

<main id="main" class="site-main">
    <div class="rooms-archive">
        <div class="container">
            <header class="archive-header">
                <h1 class="archive-title">Our Rooms</h1>
                <p class="archive-description">Discover our comfortable and stylish accommodations, each designed to provide you with the perfect stay experience.</p>
            </header>
            
            <?php if (have_posts()) : ?>
                <div class="rooms-grid">
                    <?php while (have_posts()) : the_post(); 
                        // Get room fields
                        $room_color = get_field('room_color');
                        $room_title = get_field('room_title') ?: get_the_title();
                        $room_description = get_field('room_description');
                        $room_gallery = get_field('room_gallery');
                        $room_amenities = get_field('room_amenities');
                        $room_price = get_field('room_price');
                        $room_capacity = get_field('room_capacity');
                        $room_size = get_field('room_size');
                        
                        // Get featured image as fallback
                        $featured_image = get_the_post_thumbnail_url(get_the_ID(), 'large');
                        $main_image = !empty($room_gallery) ? $room_gallery[0]['sizes']['large'] : $featured_image;

                        // Get room slug for hashtag link
                        $room_slug = get_post_field('post_name', get_the_ID());
                        $rooms_page_url = '/rooms/'; // Adjust this to your actual rooms page URL
                        $room_link = $rooms_page_url . '#' . $room_slug;
                    ?>
                        <article class="room-card" style="<?php echo $room_color ? '--room-color: ' . esc_attr($room_color) . ';' : ''; ?>">
                            <?php if ($main_image) : ?>
                                <div class="room-image">
                                    <a href="<?php echo esc_url($room_link); ?>" class="hashtagLink">
                                        <img src="<?php echo esc_url($main_image); ?>" alt="<?php echo esc_attr($room_title); ?>" loading="lazy">
                                    </a>
                                    <?php if (!empty($room_gallery) && count($room_gallery) > 1) : ?>
                                        <div class="room-gallery-indicator">
                                            <span class="gallery-count"><?php echo count($room_gallery); ?> photos</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <div class="room-content">
                                <div class="room-header">
                                    <h2 class="room-title">
                                        <a href="<?php echo esc_url($room_link); ?>" class="hashtagLink"><?php echo esc_html($room_title); ?></a>
                                    </h2>
                                    <?php if ($room_price) : ?>
                                        <div class="room-price">
                                            <span class="price-amount">€<?php echo number_format($room_price, 2); ?></span>
                                            <span class="price-period">/night</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if ($room_description) : ?>
                                    <div class="room-excerpt">
                                        <?php 
                                        $excerpt = wp_strip_all_tags($room_description);
                                        echo esc_html(wp_trim_words($excerpt, 25, '...'));
                                        ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="room-details">
                                    <?php if ($room_capacity) : ?>
                                        <div class="room-detail">
                                            <span class="detail-icon">👥</span>
                                            <span class="detail-text"><?php echo esc_html($room_capacity); ?> guests</span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($room_size) : ?>
                                        <div class="room-detail">
                                            <span class="detail-icon">📐</span>
                                            <span class="detail-text"><?php echo esc_html($room_size); ?>m²</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <?php if (!empty($room_amenities) && is_array($room_amenities) && count($room_amenities) > 0) : ?>
                                    <div class="room-amenities-preview">
                                        <div class="amenities-list">
                                            <?php 
                                            $displayed_amenities = array_slice($room_amenities, 0, 3);
                                            foreach ($displayed_amenities as $amenity) : ?>
                                                <span class="amenity-tag"><?php echo esc_html($amenity['amenity_name']); ?></span>
                                            <?php endforeach; ?>
                                            
                                            <?php if (count($room_amenities) > 3) : ?>
                                                <span class="amenity-more">+<?php echo count($room_amenities) - 3; ?> more</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="room-actions">
                                    <a href="<?php echo esc_url($room_link); ?>" class="btn btn-primary hashtagLink">View Details</a>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>
                
                <?php
                // Pagination
                the_posts_pagination(array(
                    'mid_size' => 2,
                    'prev_text' => __('← Previous'),
                    'next_text' => __('Next →'),
                ));
                ?>
                
            <?php else : ?>
                <div class="no-rooms-message">
                    <h2>No rooms found</h2>
                    <p>We don't have any rooms available at the moment. Please check back later.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</main>

<style>
.rooms-archive {
    padding: 4rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.archive-header {
    text-align: center;
    margin-bottom: 4rem;
}

.archive-title {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 1rem 0;
}

.archive-description {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.rooms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.room-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.room-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--room-color, #3498db);
}

.room-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.room-image a {
    display: block;
    width: 100%;
    height: 100%;
}

.room-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.room-card:hover .room-image img {
    transform: scale(1.08);
}

.room-gallery-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.room-content {
    padding: 2rem;
}

.room-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.room-title {
    margin: 0;
    flex: 1;
}

.room-title a {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--room-color, #2c3e50);
    text-decoration: none;
    line-height: 1.3;
}

.room-title a:hover {
    color: var(--room-color, #3498db);
}

.room-price {
    text-align: right;
    white-space: nowrap;
}

.price-amount {
    font-size: 1.25rem;
    font-weight: 800;
    color: var(--room-color, #2c3e50);
}

.price-period {
    font-size: 0.9rem;
    color: #666;
}

.room-excerpt {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.room-details {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.room-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.detail-icon {
    font-size: 1rem;
    opacity: 0.8;
}

.room-amenities-preview {
    margin-bottom: 1.5rem;
}

.amenities-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.amenity-tag {
    background: #f8f9fa;
    color: #666;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.amenity-more {
    background: var(--room-color, #3498db);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.room-actions {
    text-align: center;
}

.btn {
    display: inline-block;
    padding: 0.875rem 2rem;
    background: var(--room-color, #3498db);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    filter: brightness(1.1);
}

.no-rooms-message {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.no-rooms-message h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #333;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 3rem;
}

.pagination .page-numbers {
    padding: 0.75rem 1rem;
    background: white;
    color: #666;
    text-decoration: none;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.pagination .page-numbers:hover,
.pagination .page-numbers.current {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .rooms-archive {
        padding: 2rem 0;
    }
    
    .archive-header {
        margin-bottom: 2rem;
    }
    
    .archive-title {
        font-size: 2rem;
    }
    
    .rooms-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .room-header {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
    
    .room-price {
        text-align: left;
    }
    
    .room-details {
        flex-direction: column;
        gap: 0.75rem;
    }
}
</style>

<?php get_footer(); ?>
