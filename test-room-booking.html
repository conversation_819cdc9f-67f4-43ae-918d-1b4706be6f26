<!DOCTYPE html>
<html>
<head>
    <title>Test Room Booking Links</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 40px; }
        .test-link { 
            display: block; 
            margin: 10px 0; 
            padding: 15px; 
            background: #E55D2D; 
            color: white; 
            text-decoration: none; 
            border-radius: 6px;
            width: 300px;
            text-align: center;
        }
        .test-link:hover { background: #d14a26; }
        h2 { color: #232020; margin-top: 30px; }
    </style>
</head>
<body>
    <h1>Test Room Booking Links</h1>
    
    <h2>Room Name Links:</h2>
    <a href="#book?room=boho" class="test-link">Book Boho Room</a>
    <a href="#book?room=deco" class="test-link">Book Deco Room</a>
    <a href="#book?room=retro" class="test-link">Book Retro Room</a>
    <a href="#book?room=indo" class="test-link">Book Indo Room</a>
    
    <h2>Room ID Links:</h2>
    <a href="#book?room=67441" class="test-link">Book Room 67441 (Boho)</a>
    <a href="#book?room=67440" class="test-link">Book Room 67440 (Deco)</a>
    <a href="#book?room=67439" class="test-link">Book Room 67439 (Retro)</a>
    <a href="#book?room=67436" class="test-link">Book Room 67436 (Indo)</a>
    
    <h2>General Booking:</h2>
    <a href="#book" class="test-link">General Booking (Restaurant Tab)</a>
    
    <script>
        // Test if click handlers work
        document.addEventListener('click', function(e) {
            if (e.target.matches('a[href^="#book"]')) {
                console.log('Native click detected on:', e.target.href);
            }
        });
    </script>
</body>
</html>
