<?php
/**
 * Template part for displaying the sticky WhatsApp button
 */

// Get WhatsApp settings from ACF options
$whatsapp_enabled = get_field('whatsapp_enable', 'option');

// Only proceed if WhatsApp button is enabled and profile image is set
if ($whatsapp_enabled && !empty(get_field('whatsapp_profile_image', 'option'))) :
    $whatsapp_number = get_field('whatsapp_number', 'option');
    $whatsapp_profile_image = get_field('whatsapp_profile_image', 'option');
    $whatsapp_available = get_field('whatsapp_available', 'option');
    $whatsapp_message_raw = get_field('whatsapp_message', 'option');
    $whatsapp_message = wp_strip_all_tags($whatsapp_message_raw);
    // Prepare the WhatsApp URL with the message
    $whatsapp_url = 'https://wa.me/' . esc_attr($whatsapp_number);

    if ($whatsapp_message) {
        $whatsapp_url .= '?text=' . urlencode($whatsapp_message);
    }

    // Get profile image
    $profile_image_url = $whatsapp_profile_image['sizes']['thumbnail'];
    $profile_image_alt = $whatsapp_profile_image['alt'] ? $whatsapp_profile_image['alt'] : 'WhatsApp Profile';
    $size = 'thumbnail';
?>
<div class="stickyWhatsapp">

    <a href="<?php echo esc_url($whatsapp_url); ?>" class="stickyWhatsappButton" target="_blank" rel="noopener noreferrer" aria-label="Contact via WhatsApp<?php echo esc_html($whatsapp_available) ? ' - Available now' : ''; ?>">
        <span class="profileImageWrapper">
            <img src="<?php echo esc_url($profile_image_url); ?>" alt="<?php echo esc_attr($profile_image_alt); ?>" class="profileImage">
        </span>
        <?php if ($whatsapp_available) : ?>
            <span class="availabilityIndicator">
                <i class="icon-phone"></i>
            </span>
        <?php endif; ?>
    </a>
</div>
<?php endif; ?>
