<?php
/**
 * Template part for displaying the popup
 */

// Get the active popup
$args = array(
    'post_type' => 'popup',
    'posts_per_page' => 1,
    'meta_query' => array(
        array(
            'key' => 'popup_enabled',
            'value' => '1',
            'compare' => '='
        )
    )
);

$popup_query = new WP_Query($args);

if ($popup_query->have_posts()) :
    while ($popup_query->have_posts()) : $popup_query->the_post();

        // Get popup data
        $popup_id = get_the_ID();
        $popup_title = get_field('popup_title');
        $popup_text = get_field('popup_text');
        $media_type = get_field('popup_media_type');
        $popup_image = get_field('popup_image');
        $video_type = get_field('popup_video_type');
        $popup_video = get_field('popup_video');
        $popup_video_url = get_field('popup_video_url');
        $popup_buttons = get_field('popup_buttons');
        $expiry_days = get_field('popup_expiry_days') ? get_field('popup_expiry_days') : 2;
?>
<div id="sitePopup" class="sitePopup" data-popup-id="<?php echo esc_attr($popup_id); ?>" data-expiry-days="<?php echo esc_attr($expiry_days); ?>">
    <div class="popupOverlay"></div>
    <div class="popupContent">
        <div class="popupClose">
            <span class="closeIcon">×</span>
        </div>

        <div class="popupInner">
            <?php if ($media_type == 'image' && $popup_image) : ?>
            <div class="popupMedia popupImage">
                <img src="<?php echo esc_url($popup_image['url']); ?>" alt="<?php echo esc_attr($popup_image['alt']); ?>" />
            </div>
            <?php elseif ($media_type == 'video') : ?>
            <div class="popupMedia popupVideo">
                <?php if ($video_type == 'file' && $popup_video) : ?>
                <video class="video" muted playsinline loop autoplay controls>
                    <source src="<?php echo esc_url($popup_video['url']); ?>" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
                <?php elseif ($video_type == 'url' && $popup_video_url) : ?>
                <?php
                    // Check if it's a YouTube URL
                    if (strpos($popup_video_url, 'youtube.com') !== false || strpos($popup_video_url, 'youtu.be') !== false) :
                        // Extract YouTube video ID
                        preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/ ]{11})/', $popup_video_url, $matches);
                        $youtube_id = isset($matches[1]) ? $matches[1] : '';
                        if ($youtube_id) :
                ?>
                <iframe width="100%" height="100%" src="https://www.youtube.com/embed/<?php echo esc_attr($youtube_id); ?>?autoplay=1&mute=1&controls=1&rel=0&enablejsapi=1" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                <?php
                        endif;
                    // Check if it's a Vimeo URL
                    elseif (strpos($popup_video_url, 'vimeo.com') !== false) :
                        // Extract Vimeo video ID
                        preg_match('/vimeo\.com\/(?:video\/)?([0-9]+)/', $popup_video_url, $matches);
                        $vimeo_id = isset($matches[1]) ? $matches[1] : '';
                        if ($vimeo_id) :
                ?>
                <iframe src="https://player.vimeo.com/video/<?php echo esc_attr($vimeo_id); ?>?autoplay=1&loop=1&title=0&byline=0&portrait=0&api=1" width="100%" height="100%" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>
                <?php
                        endif;
                    // If it's a direct video URL
                    else :
                ?>
                <video class="video" muted playsinline loop autoplay controls>
                    <source src="<?php echo esc_url($popup_video_url); ?>" type="video/mp4">
                    Your browser does not support the video tag.
                </video>
                <?php endif; ?>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <div class="popupText">
                <?php if ($popup_title) : ?>
                <h2 class="bigTitle"><?php echo esc_html($popup_title); ?></h2>
                <?php endif; ?>

                <?php if ($popup_text) : ?>
                <div class="popupDescription">
                    <?php echo $popup_text; ?>
                </div>
                <?php endif; ?>

                <?php if ($popup_buttons && count($popup_buttons) > 0) : ?>
                <div class="popupButtons">
                    <?php foreach ($popup_buttons as $button) : ?>
                    <a class="button primary" target="_blank" href="<?php echo esc_url($button['button_url']); ?>" title="<?php echo esc_html($button['button_text']); ?>">
                        <span class="innerTextWrapper">
                            <span class="absoluteText"><?php echo esc_html($button['button_text']); ?></span>
                            <span class="absoluteText" aria-hidden="true"><?php echo esc_html($button['button_text']); ?></span>
                            <span class="innerText" aria-hidden="true"><?php echo esc_html($button['button_text']); ?></span>
                        </span>
                    </a>
                    <?php endforeach; ?>
                </div>
                <?php else : ?>
                    <?php include("parts/contact-buttons.php"); ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php
    endwhile;
    wp_reset_postdata();
endif;
?>
