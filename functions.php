<?php

// Include centralized ACF management system
require_once get_template_directory() . '/acf.php';

// Include additional settings
require_once get_template_directory() . '/includes/whatsapp-settings.php';
require_once get_template_directory() . '/includes/add-gallery-field.php';
require_once get_template_directory() . '/includes/opening-hours-functions.php';

function jn_enqueue_assets() {
  $scripts = array(
    'Swup' => '/libs/swup.js',
    'Swup_head' => '/libs/swup_head.js',
    'Swup_Gtag' => '/libs/swup_gtag.js',
    'Swup GA' => '/libs/SwupGaPlugin.min.js',
    'Jquery' => '/libs/jquery.min.js',
    'Lenis' => '/libs/lenis.min.js',
    // 'Select2' => '/libs/select2.min.js',
    'Gsap' => '/libs/gsap.min.js',
    'Custom_Ease' => '/libs/CustomEase.min.js',
    'ScrollTrigger' => '/libs/ScrollTrigger.min.js',
    'SplitText' => '/libs/SplitText.min.js',
    'Flickity_js' => '/libs/flickity.min.js',
    'Hammer_js' => '/libs/hammer.min.js',
    'main_js' => '/assets/js/main.js',
    'Header' => '/assets/js/header.js',
    'Footer_JS' => '/assets/js/parts/footer.js',
    'Inview' => '/assets/js/parts/inview.js',
    'Footer' => '/assets/js/footer.js',
    'Gallery' => '/assets/js/parts/gallery.js',
    'Slider' => '/assets/js/parts/slider.js',
    'parallax' => '/assets/js/parts/parallax.js',
    'split' => '/assets/js/parts/split.js',
    'speed' => '/assets/js/parts/form.js',
    'Popup' => '/assets/js/parts/popup.js',
    'Booking_Popup' => '/assets/js/parts/booking-popup.js',
    'Sticky' => '/assets/js/parts/sticky.js',
    'Marquee' => '/assets/js/parts/marquee.js',
    'Cursor' => '/assets/js/cursor.js',
  );

  foreach ($scripts as $handle => $path) {
    wp_enqueue_script($handle, get_theme_file_uri($path), array(), false, true);
  }

  wp_enqueue_style('main', get_stylesheet_uri());
  // wp_enqueue_style('select2', get_theme_file_uri('/libs/select2.min.css'), array(), '1.1', 'all');
}

add_action('wp_enqueue_scripts', 'jn_enqueue_assets');

function enqueue_block_scripts() {
  $scripts = array(
    'Home_Header_Block_JS' => '/blocks/js/home-header-block.js',
    'Header_Block_JS' => '/blocks/js/header-block.js',
    'big header block' => '/blocks/js/big-header-block.js',
    // 'Sticky_Big_Media_Block_JS' => '/blocks/js/sticky-big-media-block.js',
    'Images_Text_Block_JS' => '/blocks/js/images-text-block.js',
    'Rooms_Marquee_Block_JS' => '/blocks/js/rooms-marquee-block.js',
    'Rooms_Overview_Block_JS' => '/blocks/js/rooms-overview-block.js',
    'FAQ_Block_JS' => '/blocks/js/faq-block.js',
    'Contact_Block_JS' => '/blocks/js/contact-block.js',
    'PDF_Menus_Block_JS' => '/blocks/js/pdf-menus-block.js',
  );

  foreach ($scripts as $handle => $path) {
    wp_enqueue_script($handle, get_theme_file_uri($path), array(), false, true);
  }
}
add_action('wp_enqueue_scripts', 'enqueue_block_scripts');

function enqueue_block_styles() {
  $styles = array(
    'Rooms_Overview_Block_LESS' => '/blocks/less/rooms-overview-block.less',
  );

  foreach ($styles as $handle => $path) {
    wp_enqueue_style($handle, get_theme_file_uri($path), array(), false, 'all');
  }
}
add_action('wp_enqueue_scripts', 'enqueue_block_styles');

// Add favicon
function ilc_favicon() {
  echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />\n";
}

add_action('wp_head', 'ilc_favicon');

// Customize theme settings
function jn_customize_register($wp_customize) {
  $sections = array(
    'customTheme-main-callout-title' => 'Title',
    'customTheme-main-callout-description' => 'Description',
    'customTheme-main-callout-featured-image' => 'Image',
    'customTheme-main-callout-logo' => 'Logo',
    'customTheme-main-callout-logo-white' => 'Logo (White)',
    'customTheme-main-callout-telephone' => 'Telephone',
    'customTheme-main-callout-telephone-label' => 'Telephone label',
    'customTheme-main-callout-mail' => 'Mail',
    'customTheme-main-callout-address' => 'Address',
    'customTheme-main-callout-maps-link' => 'Google maps link',
    'customTheme-main-callout-facebook' => 'Facebook URL',
    'customTheme-main-callout-tiktok' => 'Tiktok URL',
    'customTheme-main-callout-instagram' => 'Instagram URL',
    'customTheme-main-callout-analytics' => 'Analytics ID',
    'customTheme-main-callout-contact-title' => 'Contact title',
    'customTheme-main-callout-contact-text' => 'Contact text',
    'customTheme-main-callout-reserve-link' => 'Reserve link (url)',
    'customTheme-main-callout-reserve-label' => 'Reserve label',
    'customTheme-main-callout-menu-image' => 'Menu image',
    'customTheme-main-callout-tripadvisor' => 'Tripadvisor URL',
    'customTheme-main-callout-tripadvisor-image' => 'Tripadvisor image',
    'customTheme-main-callout-pinterest' => 'Pinterest URL',
    // newsletter_form:
      'newsletter_form' => 'Newsletter form embed'
  );

  $wp_customize->add_section('customTheme-main-callout-section', array(
    'title' => 'Main Information'
  ));

  foreach ($sections as $setting_id => $label) {
    $wp_customize->add_setting($setting_id);
    $control_args = array(
      'label' => $label,
      'section' => 'customTheme-main-callout-section',
      'settings' => $setting_id
    );

    if (strpos($setting_id, 'featured-image') !== false || strpos($setting_id, 'logo') !== false || strpos($setting_id, 'menu-image') !== false || strpos($setting_id, 'tripadvisor-image') !== false) {
      $control_args['width'] = 750;
      $control_args['height'] = 500;
      $wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, $setting_id . '-control', $control_args));
    } elseif ($label === 'Description' || $label === 'Contact text') {
      $control_args['type'] = 'textarea';
      $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
    } else {
      $wp_customize->add_control(new WP_Customize_Control($wp_customize, $setting_id . '-control', $control_args));
    }
  }
}

add_action('customize_register', 'jn_customize_register');

// Add debug info for opening hours (only for administrators)
// add_action('wp_footer', 'debug_opening_hours');

// Register menus
function jn_register_menus() {
  register_nav_menus(array(
    'primary-menu' => 'Primary Menu',
    'secondary-menu' => 'Secondary Menu',
    'footer-menu-1' => 'Footer menu 1',
    'footer-menu-2' => 'Footer menu 2',
    'footer-menu-3' => 'Footer menu 3',
    'footer-bottom-menu' => 'Footer bottom menu'
  ));
}

add_action('after_setup_theme', 'jn_register_menus');

// Add proper CSP headers for InnStyle integration
function add_innstyle_csp_headers() {
    if (!is_admin()) {
        // Remove existing CSP headers that might conflict
        header_remove('Content-Security-Policy');

        // Add comprehensive CSP that allows InnStyle and common tracking scripts
        $csp = "frame-src 'self' https://*.innstyle.co.uk https://thefig.innstyle.co.uk; ";
        $csp .= "media-src 'self' data: blob: https://*.innstyle.co.uk https://cdn.innstyle.co.uk *.mapbox.com; ";
        $csp .= "font-src 'self' data: https://*.innstyle.co.uk https://cdn.innstyle.co.uk https://fonts.gstatic.com https://fonts.googleapis.com; ";
        $csp .= "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.innstyle.co.uk https://cdn.innstyle.co.uk https://*.googletagmanager.com https://www.google-analytics.com; ";
        $csp .= "style-src 'self' 'unsafe-inline' https://*.innstyle.co.uk https://cdn.innstyle.co.uk https://fonts.googleapis.com; ";
        $csp .= "connect-src 'self' https://*.innstyle.co.uk https://cdn.innstyle.co.uk https://*.google-analytics.com https://*.googletagmanager.com; ";
        $csp .= "img-src 'self' data: blob: https://*.innstyle.co.uk https://cdn.innstyle.co.uk https://*.google-analytics.com https://*.googletagmanager.com; ";
        $csp .= "child-src 'self' https://*.innstyle.co.uk; ";
        $csp .= "worker-src 'self' blob:;";

        header("Content-Security-Policy: " . $csp);
    }
}
add_action('send_headers', 'add_innstyle_csp_headers', 1);

// Block unwanted scripts like bandsintown
function block_unwanted_scripts() {
    // Dequeue any bandsintown or other unwanted scripts
    wp_dequeue_script('bandsintown');
    wp_dequeue_script('bandsintown-widget');
    wp_dequeue_script('widget-bandsintown');
}
add_action('wp_enqueue_scripts', 'block_unwanted_scripts', 999);

// Remove max image preview setting
add_filter('wp_robots', 'remove_max_image_preview_large', 10, 1);
function remove_max_image_preview_large($robots) {
  unset($robots['max-image-preview']);
  return $robots;
}

add_action('template_redirect', function(){
    if (is_singular('partner')) {
        // Always redirect partner singles to archive
        wp_redirect(get_post_type_archive_link('partner'), 302);
        exit;
    }
    if (is_singular('service')) {
        // Only allow detail if toggle is enabled
        $show = get_field('show_detail_page', get_queried_object_id());
        if (!$show) {
            wp_redirect(get_post_type_archive_link('service'), 302);
            exit;
        }
    }
});

// Helper function for rendering buttons (kept for backward compatibility)
function render_button($field_name) {
  $link = get_field($field_name);
  if( $link ) {
      $link_url = $link['url'];
      $link_title = $link['title'];
      $link_target = $link['target'] ? $link['target'] : '_self';
      echo '<a class="bigLink" href="' . esc_url( $link_url ) . '" title="'. esc_html( $link_title ) .'" target="' . esc_attr( $link_target ) . '"><span class="arrow"></span>' . esc_html( $link_title ) . '<span class="arrow"></span></a>';
  }
}
?>
