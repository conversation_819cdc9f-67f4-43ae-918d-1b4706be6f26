<!DOCTYPE html>
<html lang="nl" dir="ltr">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?></title>
  <meta name="robots" content="follow, index, max-snippet:-1, max-video-preview:-1, max-image-preview:large">
  <meta name="msapplication-TileColor" content="#00aba9">
  <meta name="theme-color" content="#ffffff">
  <meta name="description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="author" content="https://www.linkedin.com/in/dennisthemenace/"/>

  <meta property="og:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>" />
  <meta property="og:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>" />
  <meta property="og:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>" />
  <meta property="og:image:alt" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-title')); ?>" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>" />
  <meta property="og:site_name" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?>" />
  <meta property="og:locale" content="nl" />

  <link rel="icon" type="image/png" href="/favicon-48x48.png" sizes="48x48" />
  <link rel="shortcut icon" href="/favicon.ico" />
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <meta name="apple-mobile-web-app-title" content="Allemans" />
  <link rel="manifest" href="/site.webmanifest" />

  <link rel="canonical" href="<?php echo esc_url(get_permalink()); ?>" />
</head>

<script async src="https://www.googletagmanager.com/gtag/js?id=G-EWXT780YLB"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-EWXT780YLB');
</script>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Restaurant",
  "name": "<?php echo get_bloginfo('name'); ?>",
  "description": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-description')); ?>",
  "image": "<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>",
  "logo": "<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-logo'))); ?>",
  "url": "<?php echo esc_url(home_url()); ?>",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-address')); ?>",
    "addressLocality": "Oostrum",
    "addressCountry": "NL"
  },
  "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
    "contactType": "customer service",
    "email": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-mail')); ?>"
  },
  "sameAs": [
    "<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook')); ?>",
    "<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin')); ?>",
    "<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram')); ?>"
  ],
  "servesCuisine": "Bistro, Café",
  "acceptsReservations": "True",
  "reservationUrl": "<?php echo esc_url(get_theme_mod('customTheme-main-callout-reserve-link')); ?>"
}
</script>

  <?php wp_head(); ?>
</head>
  <body class="no-scroll">
    <?php include('template-parts/cursor.twig'); ?>
    <header id="header" class="scrolled" data-init data-delay=400>
      <div class="contentWrapper">
        <div class="col">
          <?php echo display_header_opening_hours(); ?>
        </div>
        <div class="col">
          <a class="logo" title="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> Logo" href="/">
            <svg width="167.581" height="50.507" viewBox="0 0 167.581 50.507">
              <defs>
                <clipPath>
                  <rect data-name="Rectangle 47" width="167.581" height="50.507" fill="#191816"/>
                </clipPath>
              </defs>
              <path data-name="Path 6" d="M0,2.4v8.137H8.1v41.29h8.846V10.536h8.1V2.4Z" transform="translate(0 -1.86)" fill="#191816"/>
              <path data-name="Path 7" d="M147.073,22.657h-8.744V2.4h-8.879V51.826h8.879V30.759h8.744V51.826h8.879V2.4h-8.879Z" transform="translate(-100.312 -1.86)" fill="#191816"/>
              <path data-name="Path 8" d="M274.2,51.827H295.7V43.69H283.078V31.21h10.871V22.657H283.078V10.537H295.7V2.4H274.2Z" transform="translate(-212.481 -1.86)" fill="#191816"/>
              <path data-name="Path 9" d="M447.692,51.827h8.879V31.21h10.871V22.657H456.572V10.537H469.2V2.4H447.692Z" transform="translate(-346.924 -1.86)" fill="#191816"/>
              <rect data-name="Rectangle 46" width="8.879" height="49.426" transform="translate(127.485 0.54)" fill="#191816"/>
              <g data-name="Group 31">
                <g data-name="Group 30" clip-path="url(#clip-path)">
                  <path data-name="Path 10" d="M639.292,29.35h5.627v7.079c0,2.906-.582,4.084-1.457,4.958a3.392,3.392,0,0,1-2.527.984,4.219,4.219,0,0,1-2.989-.984c-1.836-1.836-1.929-7.326-1.929-16.133s.093-14.3,1.929-16.133a3.658,3.658,0,0,1,2.764-.984c1.874,0,3.812.684,3.984,5.762l.022.652h8.844l-.021-.7C653.28,5.309,648.364,0,640.71,0a12.326,12.326,0,0,0-9.154,3.54c-4.037,4.037-4.418,9.161-4.418,21.713s.38,17.675,4.416,21.711a12.329,12.329,0,0,0,9.156,3.542,12.613,12.613,0,0,0,9.087-3.439,13.934,13.934,0,0,0,3.743-10.088V20.8H639.292Z" transform="translate(-485.979 0)" fill="#191816"/>
                </g>
              </g>
            </svg>
          </a>
        </div>
        <div class="col">
          <span class="smallText">WALK INS ONLY BEFORE 5:00PM</span>
          <!-- button: -->
           <button class="button" data-book title="BOOK">BOOK</button>
          <!-- hamburger: -->
           <div class="hamburger">
             <span class="border"></span>
             <span class="border"></span>
             <span class="border"></span>
           </div>
        </div>
      </div>
    </header>
    <div id="menu" data-lenis-prevent>
      <div class="background"></div>
      <div class="innerMenu">
        <div class="contentWrapper">
          <nav class="primary-menu">
              <?php wp_nav_menu(['theme_location' => 'primary-menu', 'container' => false, 'menu_class' => 'primaryMenu hugeTitle smaller white']); ?>
              <?php include("blocks/parts/socials.php"); ?>
          </nav>
        </div>
      </div>
    </div>
    <div id="pageContainer" class="transition-fade">
      <div class="blocks">