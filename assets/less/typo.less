// out: false
section {
  [data-lines] {
    visibility: hidden;
    .line {
      .transform(translate3d(0,0,0));
      .word {
        will-change: transform;
        .transform(translateY(@vw22));
        filter: blur(10px);
        opacity: 0;
      }
    }
  }
  &.inview {
    [data-lines] {
      visibility: visible;
      .line {
        .transform(translate3d(0,0,0));
        .word {
          .transform(translateY(0));
          filter: blur(0);
          opacity: 1;
          transition: transform 0.6s ease-in-out, opacity 0.6s ease-in-out, filter 0.6s ease-in-out;
          .stagger(100, 0.05, .45s);
        }
      }
    }
  }
}

.hugeTitle, .bigTitle, .normalTitle, .subTitle, .text, .signature {
  &.white {
    color: @almostWhite;
  }
}

.hugeTitle {
  font-family: 'Tartuffo_Trial', sans-serif;
  font-size: @vw200;
  font-weight: normal;
  line-height: 1.25;
  &.smaller {
    font-size: @vw100 + @vw40;
  }
}

.title {
  font-family: 'Tartuffo_Trial', sans-serif;
  font-size: @vw45;
  font-weight: normal;
  letter-spacing: -0.01rem;
  line-height: 1.25;
  * {
    letter-spacing: -0.01rem
  }
  &.primary {
    color: @primaryColor;
  }
  &.bigger {
    font-size: @vw60;
    * {
      letter-spacing: -0.07rem;
    }
  }
  .wrapper {
    padding-top: @vw5;
    display: inline !important;
    position: relative;
    overflow: hidden;
    will-change: transform;
  }
  .word {
    will-change: transform;
  }
  &.white {
    color: @hardWhite;
  }
}

.smallerTitle {
  font-family: 'Tartuffo_Trial', sans-serif;
  font-size: @vw35;
  font-weight: normal;
  line-height: 1.25;
  &.white {
    color: @almostWhite;
  }
}

.subTitle {
  font-size: @vw22;
  line-height: 1.2;
  font-family: "Figtree", sans-serif;
  font-weight: 200;
  letter-spacing: .125rem;
  font-style: normal;
  text-transform: uppercase;
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}


@media all and (max-width: 1080px) {
  .hugeTitle {
    font-size: @vw100-1080;
    .wrapper {
      // padding-top: @vw5-1080;
    }
  }

  .bigTitle {
    font-size: @vw50-1080;
    .wrapper {
      padding-top: @vw5-1080;
    }
  }

  .normalTitle {
    font-size: @vw34-1080;
    .wrapper {
      padding-top: @vw5-1080;
    }
  }

  .subTitle {
    font-size: @vw22-1080;
    &.smaller {
      font-size: @vw16-1080;
    }
  }

  .text {
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-1080;
      }
    }
  }

  .signature {
    font-size: @vw22-1080;
  }
}

.text {
  &.white {
    color: @almostWhite;
    p {
      color: @almostWhite;
    }
  }
}

@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: @vw70-580;
    .wrapper {
      // padding-top: @vw5-580;
    }
  }

  .bigTitle {
    font-size: @vw42-580;
    span, strong {
      display: inline-block;
      font-size: @vw45-580;
    }
    .wrapper {
      padding-top: @vw5-580;
    }
  }

  .normalTitle {
    font-size: @vw34-580;
    .wrapper {
      padding-top: @vw5-580;
    }
  }

  .subTitle {
    font-size: @vw22-580;
    &.smaller {
      font-size: @vw22-580;
    }
  }

  .text {
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-580;
      }
    }
  }

  .signature {
    font-size: @vw22-580;
  }
}
