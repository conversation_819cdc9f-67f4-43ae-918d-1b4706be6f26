// out: false
.bookingPopup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 12;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  .transition(.3s);
  
  &.active {
    opacity: 1;
    visibility: visible;
    pointer-events: all;
    
    .popupContent {
      transform: translate(-50%,-50%) scale(1);
      opacity: 1;
    }
    
    .popupOverlay {
      opacity: 1;
    }
  }

  .popupOverlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    cursor: pointer;
    opacity: 0;
    .transition(.3s);
  }

  .popupContent {
    position: absolute;
    top: 50%;
    left: 50%;
    background: @hardWhite;
    border-radius: @vw20;
    width: calc((@vw200 * 3) + (@vw20 * 3));
    max-width: 90vw;
    max-height: 90vh;
    height: calc(100% ~"-" @vw200 ~"-" @vw60);
    overflow: hidden;
    transform: translate(-50%,-50%) scale(.8);
    opacity: 0;
    .transition(.4s, cubic-bezier(0.175, 0.885, 0.32, 1.275));
  }

  .popupClose {
    position: absolute;
    top: @vw20;
    right: @vw20;
    z-index: 2;
    cursor: pointer;

    .closeIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: @vw40;
      height: @vw40;
      border-radius: 50%;
      color: @hardBlack;
      font-size: @vw30;
      line-height: 1;
      .transition(.3s);
      &:hover {
        color: @primaryColor;
      }
    } 
  }

  .popupInner {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .tabNavigation {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: @vw40 @vw40 0 @vw40;
    gap: @vw30;
    
    .tabButton {
      font-family: 'Figtree';
      font-size: @vw32;
      font-weight: 500;
      color: @hardBlack;
      background: none;
      border: none;
      cursor: pointer;
      padding: @vw20 0;
      .transition(.3s);
      &:hover {
        color: @primaryColor;
      }
      
      &.active {
        color: @primaryColor;
      }
    }
    
    .tabSeparator {
      font-size: @vw32;
      color: @grey;
      font-weight: 300;
    }
  }

  .tabContent {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .tabPane {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    .transition(.3s);
    
    &.active {
      opacity: 1;
      visibility: visible;
    }
  }

  .tabInner {
    padding: @vw40;
    height: 100%;
    overflow-y: auto;
  }

  .loadingMessage {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    
    p {
      font-size: @vw18;
      color: @grey;
    }
  }

  .embedContainer {
    width: 100%;
    min-height: 500px;
    
    iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
  }
}

// Responsive styles
@media screen and (max-width: 1080px) {
  .bookingPopup {
    .popupContent {
      border-radius: @vw20-1080;
      width: calc((@vw200-1080 * 3) + (@vw20-1080 * 3));
    }

    .popupClose {
      top: @vw20-1080;
      right: @vw20-1080;

      .closeIcon {
        width: @vw40-1080;
        height: @vw40-1080;
        font-size: @vw30-1080;
      }
    }

    .tabNavigation {
      padding: @vw40-1080 @vw40-1080 0 @vw40-1080;
      gap: @vw30-1080;
      
      .tabButton {
        font-size: @vw32-1080;
        padding: @vw20-1080 0;
      }
      
      .tabSeparator {
        font-size: @vw32-1080;
      }
    }

    .tabInner {
      padding: @vw40-1080;
    }

    .loadingMessage {
      p {
        font-size: @vw18-1080;
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .bookingPopup {
    .popupContent {
      border-radius: @vw20-580;
      width: calc((@vw200-580 * 3) + (@vw20-580 * 3));
      max-width: 95vw;
    }

    .popupClose {
      top: @vw20-580;
      right: @vw20-580;

      .closeIcon {
        width: @vw40-580;
        height: @vw40-580;
        font-size: @vw30-580;
      }
    }

    .tabNavigation {
      padding: @vw40-580 @vw40-580 0 @vw40-580;
      gap: @vw30-580;
      
      .tabButton {
        font-size: @vw28-580;
        padding: @vw20-580 0;
      }
      
      .tabSeparator {
        font-size: @vw28-580;
      }
    }

    .tabInner {
      padding: @vw30-580;
    }

    .loadingMessage {
      height: 150px;
      
      p {
        font-size: @vw16-580;
      }
    }

    .embedContainer {
      min-height: 400px;
    }
  }
}
