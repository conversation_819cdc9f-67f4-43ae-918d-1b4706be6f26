// out: false
.whiteSpaceWrapper {
  display: none; // No longer needed since footer is not fixed
}

.footer {
  padding-top: @vw100 + @vw50;
  padding-bottom: @vw30;
  background: @almostBlack;
  color: @almostWhite;
  // height: calc(100vh ~"-" @vw100 ~"-" @vw50);
  height: 100vh;
  min-height: @vw100 * 9;
  position: relative; // Changed from fixed to relative
  width: 100%;
  .contentWrapper {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }
  .topFooter {
    margin-bottom: @vw20;
    .cols {
      .col {
        width: calc(25% ~"-" @vw20);
        &:first-child {
          width: calc(50% ~"-" @vw20);
          padding-right: @vw200 + @vw20 + @vw20;
        }
      }
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22;
      }
    }
  }
  // newsletter styling:
  .wpcf7-form {
    position: relative;
    margin-top: @vw30;
    .wpcf7-form-control-wrap {
      &:after {
        content: '\e90a';
        position: absolute;
        top: 50%;
        pointer-events: none;
        font-family: 'Icomoon';
        .transform(translateY(-50%));
        right: 0;
        color: @almostWhite;
      }
    }
    input, textarea {
      width: 100%;
      padding: @vw10;
      border: none;
      border-bottom: 1px solid @almostWhite;
      .rounded(0);
      font-size: @vw18;
      font-family: 'Figtree', Arial, sans-serif;
      box-sizing: border-box;
      background: transparent;
      color: @almostWhite;
      &[type="submit"] {
        height: @vw40;
        line-height: @vw35;
        position: absolute;
        top: 0;
        padding: @vw5 @vw30;
        right: 0;
        opacity: 0;
        cursor: pointer;
        background: transparent;
        color: @almostBlack;
        width: auto;
        z-index: 20;
      }
      &:focus {
        border-color: none;
        outline: none;
      }

      &::-webkit-input-placeholder {
        color: #999;
      }

      &:-moz-placeholder {
          color: #999;
      }

      &::-moz-placeholder {
          color: #999;
      }

      &:-ms-input-placeholder {
          color: #999;
      }
    }
    .wpcf7-form-control-wrap {
      margin-bottom: @vw20;
      &:last-child {
        margin-bottom: 0;
      }
      label {
        display: block;
        margin-bottom: @vw10;
      }
      textarea {
        resize: none;
      }
      .wpcf7-submit {
        display: block;
        width: 100%;
        padding: @vw10;
        border: none;
        .rounded(@vw10);
        font-size: @vw18;
        font-family: 'Figtree', Arial, sans-serif;
        box-sizing: border-box;
        background: @primaryColor;
        color: @hardWhite;
        cursor: pointer;
        .transition(.3s);
        &:hover {
          background: @hardWhite;
          color: @primaryColor;
        }
      }
    }
  }
  .middleFooter {
    .cols {
      .col {
        vertical-align: bottom;
        width: calc(25% ~"-" @vw20);
        &:first-child {
          width: calc(75% ~"-" @vw20);
          padding-right: @vw200 + @vw20 + @vw20;
        }
        &:last-child {
          display: inline-flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }
        svg {
          width: 100%;
          height: auto;
          object-fit: contain;
        }
      }
    }
  }
  .socials {
    .social {
      display: inline-block;
      &:not(:last-child) {
        margin-right: @vw30;
      }
    }
  }
  .tripAdvisor {
    display: inline-block;
    width: @vw100;
    height: @vw100;
    cursor: pointer;
    .transitionMore(opacity, .3s, 0s, ease-in-out);
    &:hover {
      opacity: .5;
    }
    img {
      width: 100%;
      height: auto;
      object-fit: contain;
      pointer-events: none;
    }
  }
  .divider {
    height: 1px;
    background: @almostWhite;
    width: 100%;
    margin: @vw40 0;
  }
  .title {
    margin-bottom: @vw20;
  }
  .cols {
    margin-left: -@vw10;
    width: calc(100% ~"+" @vw20);
    .col {
      display: inline-block;
      margin: 0 @vw10;
      vertical-align: top;
      nav {
        ul {
          list-style: none;
        }
        li {
          a {
            color: @hardBlack;
            .transition(.3s);
            &:hover {
              opacity: .5;
            }
          }
        }
      }
    }
  }
  .topFooter {
    .col {
      width: calc(25% ~"-" @vw16);
    }
    nav {
      ul {
        li {
          &:not(:last-child) {
            margin-bottom: @vw16;
          }
        }
      }
    }
    .contactDetails {
      display: block;
      width: (@vw112 * 2) + @vw16;
      max-width: 100%;
      .links {
        margin: @vw30 0;
        .link {
          &:not(:last-child){
            margin-bottom: @vw10;
          }
          display: table;
          text-decoration: none;
          color: @hardBlack;
          cursor: pointer;
          .transition(.3s);
          &:hover {
            opacity: .7;
          }
          i {
            margin-right: @vw5;
          }
          span {
            text-decoration: underline;
          }
        }
      }
    }
    .socials {
      margin-top: @vw30;
    }
  }
  .bottomFooter {
    font-size: @vw16;
    text-align: center;
    .col {
      width: calc(25% ~"-" @vw16);
      vertical-align: middle;
      &:nth-child(2) { 
        width: calc(50% ~"-" @vw16);
      }
    }
    li {
      display: inline-block;
      vertical-align: middle;
    }
    .smallCol {
      display: inline-block;
      width: 50%;
      vertical-align: middle;
      &:nth-child(2) {
        text-align: right;
      }
    }
    .innerDivider {
      display: inline-block;
      margin: 0 @vw5;
      vertical-align: middle;
      opacity: .2;
    }
    .divider {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      .innerCol {
        display: inline-block;
        position: relative;
        &:nth-child(1), &:nth-child(3) {
          width: 100%;
          .innerBar {
            background: @hardBlack;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 0;
            width: 100%;
            height: 1px;
          }
        }
        &:nth-child(1) {
          .innerBar {
            right: 0;
            left: auto;
            -webkit-mask-image: linear-gradient(-90deg, rgba(0,0,0,1), rgba(0,0,0,0));
            mask-image: linear-gradient(-90deg, rgba(0,0,0,1), rgba(0,0,0,0));
          }
        }
        &:nth-child(2) {
          text-align: center;
          width: @vw60 + @vw5;
        }
        &:nth-child(3) {
          .innerBar {
            -webkit-mask-image: linear-gradient(90deg, rgba(0,0,0,1), rgba(0,0,0,0));
            mask-image: linear-gradient(90deg, rgba(0,0,0,1), rgba(0,0,0,0));
          }
        }
        i {
          display: block;
          font-size: @vw16;
          color: @hardBlack;
        }
      }
    }
    .items {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      gap: @vw10;
      .innerDivider {
        height: @vw22;
        width: 1px;
        background: rgba(245, 241, 234, .7);
      }
    }
  }
}

@media all and (max-width: 1080px) {
  .footer {
    padding-top: @vw100-1080 + @vw50-1080;
    padding-bottom: @vw30-1080;
    .cols {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .col {
        margin: 0 @vw8-1080;
        nav {
          ul {
            li {
              a {
                &:hover {
                  opacity: .5;
                }
              }
            }
          }
        }
      }
    }
    .topFooter {
      .col {
        width: calc(25% ~"-" @vw16-1080);
      }
      nav {
        ul {
          li {
            &:not(:last-child) {
              margin-bottom: @vw16-1080;
            }
          }
        }
      }
      .contactDetails {
        width: (@vw112-1080 * 2) + @vw16-1080;
        .links {
          margin: @vw30-1080 0;
          .link {
            &:not(:last-child) {
              margin-bottom: @vw10-1080;
            }
            i {
              margin-right: @vw5-1080;
            }
          }
        }
      }
      .socials {
        margin-top: @vw30-1080;
      }
    }
    .bottomFooter {
      font-size: @vw16-1080;
      padding-top: @vw80-1080;
      .col {
        width: calc(25% ~"-" @vw16-1080);
        &:nth-child(2) {
          width: calc(50% ~"-" @vw16-1080);
        }
      }
      .innerDivider {
        margin: 0 @vw5-1080;
      }
      .divider {
        .innerCol {
          &:nth-child(2) {
            width: @vw60-1080 + @vw5-1080;
          }
          i {
            font-size: @vw16-1080;
          }
        }
      }
      .bigLogoWrapper {
        width: (@vw112-1080 * 6) + (@vw16-1080 * 5);
      }
    }
  }
}

@media all and (max-width: 580px) {
  .whiteSpaceWrapper {
    display: none;
  }
  .footer {
    position: relative;
    padding-top: @vw100-580 + @vw50-580;
    padding-bottom: @vw30-580;
    .cols {
      margin-bottom: -@vw30-580;
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        margin-bottom: @vw30-580;
        nav {
          ul {
            li {
              a {
                &:hover {
                  opacity: .5;
                }
              }
            }
          }
        }
      }
    }
    .topFooter {
      .col {
        width: calc(50% ~"-" @vw16-580);
      }
      nav {
        ul {
          li {
            &:not(:last-child) {
              margin-bottom: @vw16-580;
            }
          }
        }
      }
      .contactDetails {
        width: (@vw112-580 * 2) + @vw16-580;
        .links {
          margin: @vw30-580 0;
          .link {
            &:not(:last-child) {
              margin-bottom: @vw10-580;
            }
            i {
              margin-right: @vw5-580;
            }
          }
        }
      }
      .socials {
        margin-top: @vw30-580;
      }
    }
    .bottomFooter {
      font-size: @vw22-580;
      padding-top: @vw80-580;
      .col {
        text-align: center;
        width: calc(100% ~"-" @vw16-580);
        &:nth-child(2) {
          width: calc(100% ~"-" @vw16-580);
        }
      }
      .innerDivider {
        margin: 0 @vw5-580;
      }
      .divider {
        .innerCol {
          &:nth-child(2) {
            width: @vw60-580 + @vw5-580;
          }
          i {
            font-size: @vw16-580;
          }
        }
      }
      .bigLogoWrapper {
        top: -30%;
        z-index: 0;
        width: (@vw112-580 * 6) + (@vw16-580 * 5);
      }
    }
  }
}
