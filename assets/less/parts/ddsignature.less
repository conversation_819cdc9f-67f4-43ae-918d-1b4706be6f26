// out: false
.signatureDD {
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    color: #000000;
    &:hover { 
        opacity: .4;
        transition: opacity .3s;
    }
    .linkDD {
        color: #000000;
        cursor: pointer;
        font-size: @vw16;
        display: inline-block;
        text-decoration: none;
    }
    .innerTextDD {
        margin-right: @vw5;
    }
    .innerTextDD, .svgWrap {
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
    }
    .svgWrap { 
        cursor: pointer;
        display: inline-block;
        vertical-align: middle;
        width: @vw100;
        height: auto;
        svg {
            cursor: pointer;
            display: inline-block;
            vertical-align: middle;
            width: 100%;
            height: auto;
            object-fit: contain;
        }
    }
}

@media all and (max-width: 1080px) {
    .signatureDD {
        .linkDD {
            font-size: @vw16-1080;
        }
        .svgWrap { 
            width: @vw80-1080;
        }
        .innerTextDD {
            margin-right: @vw5-1080;
        }
    }
}

@media all and (max-width: 580px) {
    .signatureDD {
        .linkDD {
            font-size: @vw22-580;
        }
        .svgWrap { 
            width: @vw100-580;
        }
        .innerTextDD {
            margin-right: @vw5-580;
        }
    }
}