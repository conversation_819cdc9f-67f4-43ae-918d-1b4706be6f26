// out: false

body {
  &:has(.homeHeaderBlock) {
    header {
      &.showLogo {
        .logo {
          .filter(blur(0px));
          opacity: 1;
          transition: filter 0.45s 0s ease-in-out, opacity 0.45s 0s ease-in-out;
          -webkit-transition: filter 0.45s 0s ease-in-out, opacity 0.45s 0s ease-in-out;
        }
      }
      .logo {
        .filter(blur(10px));
        opacity: 0;
        transition: transform 0.3s 0s ease-in-out, opacity 0.3s 0s ease-in-out;
        -webkit-transition: transform 0.3s 0s ease-in-out, opacity 0.3s 0s ease-in-out;
      }
    }
  }
}
#header {
  position: fixed;
  padding: @vw20 0;
  background-color: @almostWhite;
  top: 0;
  left: 0;
  z-index: 11;
  overflow: hidden;
  transition: background-color 0.3s ease-in-out, padding 0.3s ease-in-out;
  -webkit-transition: background-color 0.3s ease-in-out, padding 0.3s ease-in-out;
  border-bottom: 1px solid rgba(15, 15, 14, 0.05);
  &.hide {
    transform: translateY(-100%);
    .transition(.3s);
  }
  &.scrolled {
    // padding: @vw20 0;
    &:after {
      // opacity: 1;
    }
    .logo {
      img {
        opacity: 1;
        &.white {
          opacity: 0;
        }
      }
    }
    .hamburger {
      border-color: @hardBlack;
      .border {
        background: @hardBlack;
      }
    }
  }
  &.active {
    .col {
      .transform(translateY(0));
      opacity: 1;
      .transitionMore(all, .6s, 0s);
      .stagger(3, 0.15, 0s);
    }
  }
  &.openMenu {
    background: @almostBlack;
    color: @almostWhite;
    .logo {
      opacity: 1 !important;
      .filter(blur(0px)) !important;
      svg {
        path, rect {
          fill: @almostWhite;
        }
      }
    }
    .hamburger {
      border-color: @almostWhite;
      .border {
        background: @almostWhite;
        &:nth-child(1) {
          transform: translateY(@vw8) rotate(45deg);
        }
        &:nth-child(2) {
          width: 0%;
        }
        &:nth-child(3) {
          transform: translateY(-@vw10) rotate(-45deg);
        }
      }
    }
    .button {
      background: @almostWhite !important;
      color: @almostBlack !important;
      border-color: @almostWhite !important;
      &:hover {
        color: @almostWhite !important;
        background: @almostBlack !important;
      }
    }
  }
  .col {
    display: inline-block;
    vertical-align: middle;
    width: 40%;
    .transform(translateY(-@vw22));
    opacity: 0;
    &:nth-child(2) {
      text-align: center;
      width: 20%;
    }
    &:first-child {
      .opening-hours-status {
        display: flex;
        align-items: center;
        font-size: @vw14;
        font-weight: 500;
        text-transform: uppercase;
        span {
          letter-spacing: 0.08rem;
        }
        .status-indicator {
          width: @vw12;
          height: @vw12;
          border-radius: 50%;
          margin-right: @vw8;
          flex-shrink: 0;
        }
        .status-separator {
          margin: 0 @vw8;
        }
        .opening-hours {
          font-weight: 500;
        }
        &.open {
          .status-indicator {
            background-color: #22c55e;
          }
        }
        &.closed {
          .status-indicator {
            background-color: @primaryColor;
          }
        }
      }
    }
    &:last-child {
      text-align: right;
      .button {
        margin-left: @vw20;
        text-align: center;
      }
    }
  }
  .logo {
    height: @vw50;
    width: auto;
    margin: auto;
    display: block;
    position: relative;
    svg {
      height: 100%;
      width: auto;
      display: block;
      margin: auto;
      object-fit: contain;
    }
  }
  .smallText {
    font-size: @vw14;
    letter-spacing: 0.08rem;
  }
  &:not(.openMenu) {
    .button {
      color: @almostWhite;
      background: @almostBlack;
    }
  }
  .button {
    color: @almostWhite;
    border-color: @almostBlack;
    display: inline-block;
    vertical-align: middle;
    margin-right: -1px;
    &:hover {
      color: @almostBlack;
      background: @almostWhite;
    }
  }
  .hamburger {
    cursor: pointer;
    height: @vw47;
    width: @vw63;
    border: 1px solid @almostBlack;
    display: inline-flex;
    flex-direction: column;
    padding: @vw12;
    align-items: center;
    vertical-align: middle;
    justify-content: space-between;
    * {
      cursor: pointer;
    }
    .border {
      background: @almostBlack;
      height: 2px;
      width: 100%;
      display: block;
      .transition(.3s);
    }
  }
}

#menu {
  position: fixed;
  top: 0;
  left: 0;
  height: 100lvh;
  width: 100%;
  display: flex;
  pointer-events: none;
  overflow: hidden;
  opacity: 0;
  z-index: 10;
  .transitionMore(opacity, .6s, 0s);
  text-align: center;
  &.openMenu {
    transition-delay: 0s;
    pointer-events: all;
    opacity: 1;
    .transitionMore(opacity, .3s, 0s);
    .background {
      opacity: 1;
      .transitionMore(opacity, .3s, 0s);
    }
    .innerMenu {
      ul {
        li {
          opacity: 1;
          .filter(blur(0px));
          .transitionMore(all, .6s, 0s);
          .stagger(100, 0.15, .3s);
        }
      }
    }
  }
  .innerMenu {
    padding: @vw100 + @vw20 0;
    position: relative;
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    .contentWrapper {
      position: relative;
    }
    ul {
      line-height: 0.8;
      list-style: none;
      li {
        opacity: 0;
        .filter(blur(20px));
      }
    }
    .primary-menu {
      position: relative;
      height: 100%;
    }
    .primaryMenu {
      height: 100%;
      align-items: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .socials {
      position: absolute;
      right: 0;
      bottom: 0;
      .social {
        font-size: @vw30;
        &:not(:first-child) {
          margin-left: @vw20;
        }
      }
    }
  }
  .background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: @almostBlack;
    opacity: 0;
    .transitionMore(opacity, .6s, 0s);
  }
  .contentWrapper {
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
  }
  nav {
    a {
      color: @hardWhite;
      text-decoration: none;
      .transitionMore(color, .3s);
      &:hover {
        color: @primaryColor;
      }
    }
  }
  .primaryMenu {
    li {
      &:not(:last-child) {
        margin-bottom: @vw22;
      }
    }
  }
  .secondaryMenu {
    li {
      &:not(:last-child) {
        margin-bottom: @vw12;
      }
    }
  }
}

@media all and (max-width: 1080px) {
  #header {
    padding-top: @vw20-1080;
    &.scrolled {
      padding-bottom: @vw20-1080;
    }
    &.openMenu {
      .hamburger {
        .border {
          &:nth-child(1) {
            transform: translateY(@vw5-1080) rotate(45deg);
          }
          &:nth-child(3) {
            transform: translateY(-@vw5-1080) rotate(-45deg);
          }
        }
      }
    }
    .logo {
      height: @vw50-1080;
      width: calc(@vw100-1080 + @vw50-1080 + @vw5-1080);
    }
    .col {
      &:first-child {
        .opening-hours-status {
          font-size: @vw14-1080;
          .status-indicator {
            width: @vw12-1080;
            height: @vw12-1080;
            margin-right: @vw8-1080;
          }
          .status-separator {
            margin: 0 @vw8-1080;
          }
        }
      }
      &:last-child {
        .button {
          &:not(:last-child) {
            margin-right: @vw16-1080;
          }
        }
      }
    }
    .hamburger {
      height: @vw50-1080;
      width: @vw50-1080;
      padding: calc(@vw18-1080) calc(@vw14-1080);
    }
  }

  #menu {
    .background {
      .bigLogoWrapper {
        width: calc(@vw112-1080 * 6 + @vw16-1080 * 5);
      }
    }
    .primaryMenu {
      li {
        &:not(:last-child) {
          margin-bottom: @vw22-1080;
        }
      }
    }
    .secondaryMenu {
      li {
        &:not(:last-child) {
          margin-bottom: @vw12-1080;
        }
      }
    }
    .cols {
      padding: @vw100-1080 + @vw40-1080 0;
      gap: @vw40-1080;
      .innerCol {
        transform: translateY(@vw30-1080);
        &:nth-child(1) {
          padding-left: calc(@vw112-1080 + @vw16-1080);
        }
        &:nth-child(2) {
          padding-left: calc(@vw112-1080 + @vw16-1080 * 2 + @vw8-1080);
        }
      }
    }
    img {
      width: calc(@vw112-1080 + @vw16-1080 * 2);
    }
    .contactDetails {
      width: calc(@vw112-1080 * 2 + @vw16-1080);
      .links {
        margin: calc(@vw30-1080) 0;
        .link {
          &:not(:last-child) {
            margin-bottom: @vw10-1080;
          }
          i {
            margin-right: @vw5-1080;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  #header {
    padding-top: @vw20-580;
    &.scrolled {
      padding-bottom: @vw20-580;
    }
    &.openMenu {
      .button {
        &.primary {
          border-color: @hardWhite;
          color: @hardWhite;
        }
      }
      .hamburger {
        .border {
          &:nth-child(1) {
            transform: translateY(@vw5-580) rotate(45deg);
          }
          &:nth-child(3) {
            transform: translateY(-@vw5-580) rotate(-45deg);
          }
        }
      }
    }
  .logo {
    height: @vw50-580;
    width: calc(@vw100-580 + @vw50-580 + @vw5-580);
  }
  .col {
    &:first-child {
      .opening-hours-status {
        font-size: @vw12-580;
        .status-indicator {
          width: @vw10-580;
          height: @vw10-580;
          margin-right: @vw6-580;
        }
        .status-separator {
          margin: 0 @vw6-580;
        }
        .opening-hours {
          display: none; // Hide detailed hours on mobile, show only status
        }
      }
    }
    .button {
      .innerTextWrapper {
        display: none;
      }
    }
    &:last-child {
      .button {
        &:not(:last-child) {
          margin-right: @vw16-580;
        }
      }
    }
  }
  .hamburger {
    height: @vw50-580;
    width: @vw50-580;
    padding: calc(@vw18-580) calc(@vw14-580);
  }
}

  #menu {
    .background {
      .bigLogoWrapper {
        width: calc(@vw112-580 * 6 + @vw16-580 * 5);
      }
    }
    .primaryMenu {
      font-size: @vw30-580;
      li {
        &:not(:last-child) {
          margin-bottom: @vw30-580;
        }
      }
    }
    .secondaryMenu {
      li {
        &:not(:last-child) {
          margin-bottom: @vw22-580;
        }
      }
    }
    .cols {
      padding: @vw100-580 + @vw40-580 0;
      gap: @vw40-580;
      .col {
        &.top {
          vertical-align: top;
        }
        .innerCol {
          transform: translateY(@vw30-580);
          &:nth-child(1) {
            padding-left: 0;
          }
          &:nth-child(2) {
            padding-left: @vw22-580;
          }
        }
      }
    }
    img {
      width: calc(@vw112-580 + @vw16-580 * 2);
    }
    .contactDetails {
      width: calc(@vw112-580 * 2 + @vw16-580);
      .links {
        margin: calc(@vw30-580) 0;
        .link {
          &:not(:last-child) {
            margin-bottom: @vw10-580;
          }
          i {
            margin-right: @vw5-580;
          }
        }
      }
    }
  }
}
