
var pageContainerWrap;
var scroller;
var scrollerHeight = 0;
var currentScrollY = 0;
var scrollValues = {};
var dynamicScripts = [];
var popState = false;
var resizeFunction;
var inlineStyles = null;

// Add global GSAP error handling
if (typeof gsap !== 'undefined') {
    gsap.config({
        nullTargetWarn: false, // Disable warnings for null targets
        trialWarn: false
    });
}

$(document).ready(function(){
    
    if ("ontouchstart" in document.documentElement){
        $("html").addClass("touch");
    }

    if ('scrollRestoration' in history) {
        history.scrollRestoration = 'manual';
    }

    updateDynamicScriptsArray();
    
    document.addEventListener('wpcf7invalid', function (event) {
        setTimeout(function(){
            $('.wpcf7-form-control').each(function () {
                if ($(this).hasClass('wpcf7-not-valid')) {
                    $(this).closest(".field").addClass('invalid');
                } else { 
                    $(this).closest(".field").removeClass('invalid');
                }
            });
        }, 10);
    }, false);


   pageContainerWrap = new Swup({
        cache: true,
        containers: ["#pageContainer"],
        animateHistoryBrowsing: true,
        animationDuration: 300,
        plugins: [new SwupHeadPlugin({
            persistAssets: true,
            persistTags: 'style, link',
        }), new SwupGaPlugin({
            gaMeasurementId: 'G-EWXT780YLB',
        })]
    });

    // Swup hooks - fade out → wait for content → fade in when ready
    pageContainerWrap.hooks.on('animation:out:start', () => {
        // Start fade out van huidige content
        document.documentElement.classList.add('is-leaving');
    });

    pageContainerWrap.hooks.on('animation:out:end', () => {
        // Fade out is klaar
        document.documentElement.classList.remove('is-leaving');
    });

    pageContainerWrap.hooks.on('content:replace', () => {
        // Content wordt vervangen - houd onzichtbaar
        document.documentElement.classList.add('is-rendering');
    });

    pageContainerWrap.hooks.on('page:view', () => {
        // Nieuwe content is echt geladen en klaar - nu pas fade in
        setTimeout(() => {
            document.documentElement.classList.remove('is-rendering');
            document.documentElement.classList.add('is-entering');

            // Na fade in, cleanup
            setTimeout(() => {
                document.documentElement.classList.remove('is-entering');
            }, 200); // Match CSS transition duration
        }, 50); // Kleine delay om zeker te zijn dat content er is
    });


    $(document).off("click.hashtagLink", ".hashtagLink").on("click.hashtagLink", ".hashtagLink", function (e) {
        var href = $(this).attr('href');
        var hash = href.split('#')[1];

        if (hash && $("[data-anchor='" + hash + "']").length > 0) {
            e.preventDefault();

            // Check if we need to navigate to a different page first
            var currentPath = window.location.pathname;
            var targetPath = href.split('#')[0];

            if (targetPath && targetPath !== currentPath) {
                // Navigate to the target page with hash
                window.location.href = href;
                return;
            }

            // Scroll to the target on current page
            if (hash == "home") {
                scroller.scrollTo(0, {force:true});
            } else {
                var target = $("[data-anchor='" + hash + "']");
                if (target.length > 0) {
                    var headerHeight = $("header").outerHeight() || 80;
                    scroller.scrollTo(target.offset().top - headerHeight, {force:true, duration: 1.2});
                }
            }

            // Update URL without page reload
            history.pushState("", document.title, window.location.pathname + window.location.search + '#' + hash);
        }
    });
    
    pageContainerWrap.hooks.on('link:click', () => {
        scrollValues[window.location.href] = window.scrollY;
    });

    pageContainerWrap.hooks.on('history:popstate', () => {
        popState = true;
        $(document).on("initPage", function(){
            if(popState){
                window.scrollTo(0, scrollValues[window.location.href]);
                popState = false;
            }
        });
    });
    
    containerWidth = $(window).width();
    
    preloadPage();
    
    pageContainerWrap.hooks.before('content:replace', () => {
        inlineStyles = $("head style");
        $(".cmplz-optin").find("#cmplz-cookiebanner-container").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").css("opacity", "0");
        //Store the inline styles from th head, so they can be put back when in pageView event
    });
    
    
    pageContainerWrap.hooks.on('page:view', () => {
        dynamicScriptLoad();
        updateDynamicScriptsArray();
        if (inlineStyles) {
            $("head").append($(inlineStyles));
            inlineStyles = null;
        }
        setTimeout(function(){
            initPage();
            
        }, 100);
    });
    
    pageContainerWrap.hooks.on('animation:out:start', () => {
        // Fade out current page content
        document.documentElement.classList.add('is-leaving');
    });

    pageContainerWrap.hooks.on('content:replace', () => {
        // Reset scroll position and reinitialize components
        if (typeof scroller !== 'undefined') {
            scroller.scrollTo(0, {immediate: true});
        }
    });
});

function updateDynamicScriptsArray(){
    $("head script").each(function(i, el){
        if($.inArray($(el).attr("src"), dynamicScripts) == -1){
            dynamicScripts.push($(el).attr("src"));
        }
    });
}

function dynamicScriptLoad(){
    $("head script").each(function(i, el){
        if($.inArray($(el).attr("src"), dynamicScripts) == -1){
            let scriptEle = document.createElement("script");
            scriptEle.setAttribute("src", $(el).attr("src"));
            $(el).remove();
            document.head.appendChild(scriptEle);
        }
    });
    var container = $("#pageContainer")[0];
    var arr = container.getElementsByTagName('script');
    for (var n = 0; n < arr.length; n++){
        try {
            var scriptContent = arr[n].innerHTML.trim();
            if (scriptContent) {
                var scriptType = arr[n].type || 'text/javascript';

                // Only execute JavaScript, skip JSON-LD and other data types
                var isExecutableScript = (
                    (scriptType === 'text/javascript' || scriptType === '' || scriptType === 'application/javascript') &&
                    scriptType !== 'application/ld+json' &&
                    scriptType !== 'application/json' &&
                    !scriptContent.startsWith('{') &&
                    !scriptContent.startsWith('[')
                );

                if (isExecutableScript) {
                    eval(scriptContent);
                }
            }
        } catch (e) {
            console.warn('Script execution error:', e);
            console.warn('Script type:', arr[n].type);
            console.warn('Script content preview:', arr[n].innerHTML.substring(0, 100) + '...');
        }
    }
}

function initLenis(){
    scroller = new Lenis({
        easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // https://www.desmos.com/calculator/brs54l4xou
    });
    scroller.stop();
    scroller.on("scroll", function(e){
        $(document).trigger("scrollTrigger", currentScrollY);
    });
}

function raf(time) {
    scroller.raf(time);
    requestAnimationFrame(raf);
}

function preloadPage(){
    initLenis();
    initPage();
    currentScrollY = $(window).scrollTop();
    scroller.on("scroll", function(e){
        currentScrollY = $(window).scrollTop();
        ScrollTrigger.update();
    });

    requestAnimationFrame(raf);
    
    setTimeout(function(){
        $(".content").removeClass("fade");
        $("header").addClass("active");
    }, 300);
    
    
}

function initPage(){
    ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    $("a[href*=\\#]").addClass("hashtagLink");
    $("html").removeClass("stopScroll fade");
    setTimeout(function(){
        if(window.location.hash){
            var hash = window.location.hash.replace('#', '').replace('/', '');
            if($("[data-anchor='" + hash + "']").length > 0){
                var headerHeight = $("header").outerHeight() || 80;
                scroller.scrollTo($("[data-anchor='" + hash + "']").offset().top - headerHeight, {lock: 1, force:1, duration: 1.2});
                // Keep the hash in the URL for room links
                if (!hash.startsWith('book')) {
                    // Only remove hash for non-booking links
                    // history.pushState("", document.title, window.location.pathname + window.location.search);
                }
            }
        }
        if($(".instagramBlock").length > 0){
            sbi_init();
        }
    }, 150);

    pageContainerWrap.hooks.on('page:view', () => {
        setTimeout(function(){
            if(window.location.hash){
                var hash = window.location.hash.replace('#', '').replace('/', '');
                if($("[data-anchor='" + hash + "']").length > 0){
                    var headerHeight = $("header").outerHeight() || 80;
                    scroller.scrollTo($("[data-anchor='" + hash + "']").offset().top - headerHeight, {lock: 1, force:1, duration: 1.2});
                    // Keep the hash in the URL for room links
                    if (!hash.startsWith('book')) {
                        // Only remove hash for non-booking links
                        // history.pushState("", document.title, window.location.pathname + window.location.search);
                    }
                }
            }
        }, 150);
    });
    
    
    setTimeout(function(){
        $("html, body").removeClass("overflow");
        scroller.start();
        $(document).trigger("initPage"); 
        checkInviewClasses();
        $(".cmplz-optin").find("#cmplz-cookiebanner-container").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").hide();
        $(".cmplz-optin").find(".cmplz-cookiebanner").css("opacity", "0");

        setTimeout(function() {
            if ($(".cmplz-optin").length > 0) {
                show_cookie_banner();
                console.log('show cookies');
                if ($(".cmplz-cookiebanner.cmplz-dismissed").length === 0) {
                    $(".cmplz-optin").find("#cmplz-cookiebanner-container").show();
                    $(".cmplz-optin").find("#cmplz-cookiebanner-container").css("opacity", "1");
                    $(".cmplz-optin").find(".cmplz-cookiebanner").show();
                    $(".cmplz-optin").find(".cmplz-cookiebanner").css("opacity", "1");
                }
            }
        }, 450);
    }, 600);
    

    lazyLoadImages();
    checkVideos();
    disableSwupOnReserverenLinks();

    if (!$(".contactBlock").length > 0) {
        $(".flatpickr-calendar").remove();
    }

    
}

function lazyLoadImages() {
    var lazyloadImages;
    if ("IntersectionObserver" in window) {
        lazyloadImages = document.querySelectorAll(".lazy");
        const config = {
            root: null, // avoiding 'root' or setting it to 'null' sets it to default value: viewport
            rootMargin: '500px',
            threshold: 0.0
        };
        var imageObserver = new IntersectionObserver(function (entries, observer) {
            $(entries).each(function (i, entry) {
                if (entry.isIntersecting) {
                    var image = entry.target;
                    image.classList.remove("lazy");
                    image.src = image.dataset.src;
                    imageObserver.unobserve(image);
                }
            });
        }, config);
        $(lazyloadImages).each(function (i, image) {
            imageObserver.observe(image);
        });
    } else {
        $(".lazy").each(function (i, image) {
            image.classList.remove("lazy");
            image.src = image.dataset.src;
        });
    }
}

function checkVideos() {
    const videos = document.querySelectorAll("video");
    videos.forEach(video => {
      ScrollTrigger.create({
        trigger: video,
        start: "top bottom",
        end: "bottom top",
        onEnter: () => video.play() ,
        onEnterBack: () => video.play(),
        onLeave: () => video.pause(),
        onLeaveBack: () => video.pause(),
        markers: false
      });
    });
  }

  function checkInviewClasses() {
    $("[data-init]").each(function() {
      const scrollDirect = $(this).data("scroll-direct");
      ScrollTrigger.create({
        trigger: this,
        start: scrollDirect ? "0% 100%" : "0% 90%",
        end: "0% 90%",
        once: true,
        onEnter: () => {
          if ($(this).get(0).hasAttribute("data-split") && !$(this).hasClass("inview")) {
            splitLines($(this));
          }
          if ($(this).get(0).hasAttribute("data-init-delay") && !$(this).hasClass("inview")) {
            var item = $(this);
            setTimeout(function() {
              $(item).addClass("inview");
            }, $(this).data("init-delay"));
          } else {
            $(this).addClass("inview");
          }
        }
      });
    });
  }
  
  function splitLines(splitThis) {
    var split = new SplitText(splitThis, { type: "words, lines", linesClass: "wrapper", wordsClass: "word" });
    var splitText = gsap.timeline();
    splitText.from(split.words, .45, {
      y: 50,
      autoAlpha: 0,
      stagger: 0.02,
    }).to(split.words, .45, {
      y: 0,
      autoAlpha: 1
    });
  }
  
  function splitButton(button) {
    var split = new SplitText(button, { type: "letters", lettersClass: "letter" });
    var splitText = gsap.timeline();
    splitText.from(split.letters, .9, {
      y: 100,
      autoAlpha: 0,
      stagger: 0.05,
    }).to(split.letters, .9, {
      y: 0,
      autoAlpha: 1
    });
  }

  function disableSwupOnReserverenLinks() {
    $('a[href*="reserveren"]').each(function () {
      $(this).attr('data-no-swup', 'true');
    });
  }
