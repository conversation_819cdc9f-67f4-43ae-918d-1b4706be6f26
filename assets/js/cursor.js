// Custom Cursor - Simple jQuery + GSAP implementation
var cursorPos = { x: 0, y: 0 };
var cursorBorderPos = { x: 0, y: 0 };
var isHovering = false;

function initCursor() {
    if ($(window).width() <= 768 || 'ontouchstart' in window) return;

    var $cursor = $('.cursor');
    var $cursorBorder = $('.cursor-border');

    if ($cursor.length === 0 || $cursorBorder.length === 0) return;

    // Hide default cursor
    $('body').css('cursor', 'none');

    // Set initial position
    gsap.to([$cursor, $cursorBorder], {
        xPercent: -50,
        yPercent: -50,
        scale: 0,
        duration: 0.3,
        ease: "back.out(1.7)"
    });

    // Show cursors
    gsap.to([$cursor, $cursorBorder], {
        scale: 1,
        duration: 0.3,
        ease: "back.out(1.7)"
    });

    // Mouse move
    $(document).on('mousemove.cursor', function(e) {
        cursorPos.x = e.clientX;
        cursorPos.y = e.clientY;
    });

    // Hover effects
    $(document).on('mouseenter.cursor', 'a, button, .btn, input, textarea', function() {
        isHovering = true;
        gsap.to($cursor, { scale: 0.5, duration: 0.3 });
        gsap.to($cursorBorder, { scale: 1.5, duration: 0.3 });
    });

    $(document).on('mouseleave.cursor', 'a, button, .btn, input, textarea', function() {
        isHovering = false;
        gsap.to($cursor, { scale: 1, duration: 0.3 });
        gsap.to($cursorBorder, { scale: 1, duration: 0.3 });
    });

    // Click effect
    $(document).on('mousedown.cursor', function() {
        gsap.to($cursor, { scale: 0.8, duration: 0.1 });
        gsap.to($cursorBorder, { scale: 1.2, duration: 0.1 });
    });

    $(document).on('mouseup.cursor', function() {
        gsap.to($cursor, { scale: isHovering ? 0.5 : 1, duration: 0.2 });
        gsap.to($cursorBorder, { scale: isHovering ? 1.5 : 1, duration: 0.2 });
    });

    // Animation loop
    function updateCursor() {
        // Main cursor follows immediately
        gsap.to($cursor, {
            x: cursorPos.x,
            y: cursorPos.y,
            duration: 0.2,
            ease: "power2.out"
        });

        // Border cursor with delay
        cursorBorderPos.x += (cursorPos.x - cursorBorderPos.x) * 0.15;
        cursorBorderPos.y += (cursorPos.y - cursorBorderPos.y) * 0.15;

        gsap.set($cursorBorder, {
            x: cursorBorderPos.x,
            y: cursorBorderPos.y
        });

        requestAnimationFrame(updateCursor);
    }

    updateCursor();
}

function destroyCursor() {
    $('body').css('cursor', 'auto');
    $(document).off('.cursor');
}

// Initialize
$(document).ready(function() {
    initCursor();
});

// Reinitialize on Swup
$(document).on("initPage", function() {
    setTimeout(function() {
        destroyCursor();
        initCursor();
    }, 100);
});
