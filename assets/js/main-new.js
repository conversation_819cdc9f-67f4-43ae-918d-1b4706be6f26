var pageContainerWrap;
var scroller;
var scrollerHeight = 0;
var currentScrollY = 0;
var scrollValues = {};
var dynamicScripts = [];
var popState = false;
var resizeFunction;
var inlineStyles = null;

document.fonts.ready.then(function() {

    if ("ontouchstart" in document.documentElement){
        $("html").addClass("touch");
    }

    if ('scrollRestoration' in history) {
        history.scrollRestoration = 'manual';
    }

    updateDynamicScriptsArray();

    document.addEventListener('wpcf7invalid', function (event) {
        setTimeout(function(){
            $('.wpcf7-form-control').each(function () {
                if ($(this).hasClass('wpcf7-not-valid')) {
                    $(this).closest(".field").addClass('invalid');
                } else {
                    $(this).closest(".field").removeClass('invalid');
                }
            });
        }, 10);
    }, false);


    pageContainerWrap = new Swup({
        cache:true,
        containers: ["#pageContainer"],
        animateHistoryBrowsing: true,
        plugins: [new SwupHeadPlugin({
        persistAssets: true,
        persistTags: 'style link',
        }), new SwupGaPlugin({
            gaMeasurementId: '',
        })]
    });


    $(document).off("click.hashtagLink", ".hashtagLink").on("click.hashtagLink", ".hashtagLink", function (e) {
        var hash = $(this).attr('href').replace('#', '').replace('/', '');
        if($("[data-anchor='" + hash + "']").length > 0){
            gsap.to(".blocks", .3, {webkitFilter:"blur(20px)", ease: "power2.out", onComplete:function(){
                gsap.to(".blocks", .3, {webkitFilter:"blur(0px)", ease: "power2.out"});
            }});
            if(hash == "home"){
                scroller.scrollTo(0, {force:true});
            } else {
                scroller.scrollTo($("[data-anchor='" + hash + "']").offset().top - $("header .headerBar").height(), {force:true});
            }
            history.pushState("", document.title, window.location.pathname + window.location.search);
        }
    });

    pageContainerWrap.hooks.on('link:click', () => {
        scrollValues[window.location.href] = window.scrollY;
    });

    pageContainerWrap.hooks.on('history:popstate', () => {
        popState = true;
        $(document).on("initPage", function(){
            if(popState){
                window.scrollTo(0, scrollValues[window.location.href]);
                popState = false;
            }
        });
    });

    containerWidth = $(window).width();

    preloadPage();

    pageContainerWrap.hooks.before('content:replace', () => {
            //Store the inline styles from th head, so they can be put back when in pageView event
        inlineStyles = $("head style");
    });


    pageContainerWrap.hooks.on('page:view', () => {
        dynamicScriptLoad();
        updateDynamicScriptsArray();
        if (inlineStyles) {
            $("head").append($(inlineStyles));
            inlineStyles = null;
        }
        setTimeout(function(){
            initPage();

        }, 100);
    });

    pageContainerWrap.hooks.on('animation:out:start', () => {
        gsap.to(".blocks", .9, {webkitFilter:"blur(20px)", ease: "power2.out"});
    });

    pageContainerWrap.hooks.on('animation:out:end', () => {
        scroller.scrollTo(0,{offset: 0, duration:0, easing: "linear", immediate: true});
        $("header").removeClass("scrollDown");
        scroller.stop();
        scroller.start();
        $("html").addClass("stopScroll");
    });

    pageContainerWrap.hooks.before('content:replace', () => {
        $("html").addClass("stopScroll");
    });


});

function updateDynamicScriptsArray(){
    $("head script").each(function(i, el){
        if($.inArray($(el).attr("src"), dynamicScripts) == -1){
            dynamicScripts.push($(el).attr("src"));
        }
    });
}

function dynamicScriptLoad(){
    $("head script").each(function(i, el){
        if($.inArray($(el).attr("src"), dynamicScripts) == -1){
            let scriptEle = document.createElement("script");
            scriptEle.setAttribute("src", $(el).attr("src"));
            $(el).remove();
            document.head.appendChild(scriptEle);
        }
    });
    var container = $("#pageContainer")[0];
    var arr = container.getElementsByTagName('script');
    for (var n = 0; n < arr.length; n++){
        eval(arr[n].innerHTML);
    }
}

function initLenis(){
    scroller = new Lenis({
        duration: 1.5,
        easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // https://www.desmos.com/calculator/brs54l4xou
        orientation: 'vertical', // vertical, horizontal
        gestureOrientation: 'vertical', // vertical, horizontal, both
        smoothWheel: true,
        wheelMultiplier: 2,
        smoothTouch: false,
        touchMultiplier: 5,
    });
    scroller.stop();
    scroller.on("scroll", function(e){
        $(document).trigger("scrollTrigger", currentScrollY);
    });
}

function raf(time) {
    scroller.raf(time);
    ScrollTrigger.update();
    requestAnimationFrame(raf);
}

function preloadPage(){
    initLenis();
    initPage();
    currentScrollY = $(window).scrollTop();
    scroller.on("scroll", function(e){
        currentScrollY = $(window).scrollTop();
    });

    requestAnimationFrame(raf);

    setTimeout(function(){
        $(".content").removeClass("fade");
        $("header").addClass("active");
    }, 300);


}

function initPage(){
    $("a[href*=\\#]").addClass("hashtagLink");
    $("html").removeClass("stopScroll fade");
    setTimeout(function(){
        if(window.location.hash){
            var hash = window.location.hash.replace('#', '').replace('/', '');
            if($("[data-anchor='" + hash + "']").length > 0){
                scroller.scrollTo($("[data-anchor='" + hash + "']").offset().top - $("header .headerBar").height(), {lock: 1, force:1});
                history.pushState("", document.title, window.location.pathname + window.location.search);
            }
        }
        if($(".instagramWrapper").length > 0){
            sbi_init();
        }
    }, 150);

    pageContainerWrap.hooks.on('page:view', () => {
        setTimeout(function(){
            if(window.location.hash){
                var hash = window.location.hash.replace('#', '').replace('/', '');
                if($("[data-anchor='" + hash + "']").length > 0){
                    scroller.scrollTo($("[data-anchor='" + hash + "']").offset().top - $("header .headerBar").height(), {lock: 1, force:1});
                    history.pushState("", document.title, window.location.pathname + window.location.search);
                }
            }
        }, 150);
    });


    setTimeout(function(){
        $("html, body").removeClass("overflow");
        scroller.start();
        $(document).trigger("initPage");
    }, 600);


    lazyLoadImages();


}

function lazyLoadImages() {
    var lazyloadImages;
    if ("IntersectionObserver" in window) {
        lazyloadImages = document.querySelectorAll(".lazy");
        const config = {
            root: null, // avoiding 'root' or setting it to 'null' sets it to default value: viewport
            rootMargin: '500px',
            threshold: 0.0
        };
        var imageObserver = new IntersectionObserver(function (entries, observer) {
            $(entries).each(function (i, entry) {
                if (entry.isIntersecting) {
                    var image = entry.target;
                    image.classList.remove("lazy");
                    image.src = image.dataset.src;
                    imageObserver.unobserve(image);
                }
            });
        }, config);
        $(lazyloadImages).each(function (i, image) {
            imageObserver.observe(image);
        });
    } else {
        $(".lazy").each(function (i, image) {
            image.classList.remove("lazy");
            image.src = image.dataset.src;
        });
    }
}
