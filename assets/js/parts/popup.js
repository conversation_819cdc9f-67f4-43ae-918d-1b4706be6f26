$(document).ready(function(){
    $(document).on("initPage", function(){
        initSitePopup();
    });
});

function initSitePopup() {
    const popup = $('#sitePopup');

    if (popup.length === 0) {
        return;
    }

    const popupId = popup.data('popup-id');
    const expiryDays = popup.data('expiry-days') || 2;
    const popupKey = 'popup_closed_' + popupId;

    // Check if popup was previously closed
    const popupClosed = localStorage.getItem(popupKey);

    if (popupClosed) {
        // Check if the expiry time has passed
        const closedTime = parseInt(popupClosed, 10);
        const currentTime = new Date().getTime();
        const expiryTime = closedTime + (expiryDays * 24 * 60 * 60 * 1000); // Convert days to milliseconds

        if (currentTime < expiryTime) {
            // Still within expiry period, don't show popup
            return; 
        } else {
            // Expiry time has passed, remove the localStorage item
            localStorage.removeItem(popupKey);
        }
    }

    // Show popup after a short delay
    setTimeout(function() {
        popup.addClass('active');

        // Stop the scroller when popup is open
        if (typeof scroller !== 'undefined') {
            scroller.stop();
        }

        // Get video elements for later pausing
        const video = popup.find('video')[0];
        const youtubeIframe = popup.find('iframe[src*="youtube.com"]')[0];
        const vimeoIframe = popup.find('iframe[src*="vimeo.com"]')[0];

        // Close popup when clicking the close button or overlay
        popup.find('.popupClose, .popupOverlay').on('click', function() {
            closePopup(popup, popupKey, video, youtubeIframe, vimeoIframe);
        });

        // Close popup when pressing ESC key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && popup.hasClass('active')) {
                closePopup(popup, popupKey, video, youtubeIframe, vimeoIframe);
            }
        });
    }, 5000); // Show popup after 1 second
}

function closePopup(popup, popupKey, video, youtubeIframe, vimeoIframe) {
    // Save the current timestamp in localStorage
    localStorage.setItem(popupKey, new Date().getTime().toString());

    // Pause video if it exists
    if (video) {
        video.pause();
    }

    // Pause YouTube video if it exists
    if (youtubeIframe) {
        try {
            youtubeIframe.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
        } catch (e) {
            console.log('Could not pause YouTube video');
        }
    }

    // Pause Vimeo video if it exists
    if (vimeoIframe) {
        try {
            vimeoIframe.contentWindow.postMessage('{"method":"pause"}', '*');
        } catch (e) {
            console.log('Could not pause Vimeo video');
        }
    }

    // Hide popup with animation
    popup.removeClass('active');

    // Restart the scroller when popup is closed
    if (typeof scroller !== 'undefined') {
        scroller.start();
    }

    // Add a small delay before completely removing the popup from DOM
    setTimeout(function() {
        if (!popup.hasClass('active')) {
            popup.hide();
        }
    }, 500); // Wait for the fade-out animation to complete
}
