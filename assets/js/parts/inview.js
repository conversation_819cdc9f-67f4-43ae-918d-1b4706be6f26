$(document).ready(function(){
  $(document).on("initPage", function(){
      checkInviewClasses();
    });
});


function checkInviewClasses() {
    $("[data-init]").each(function(i, el) {
      const scrollDirect = $(this).data("scroll-direct");
      ScrollTrigger.create({
        trigger: this,
        start: scrollDirect ? "0% 100%" : "0% 90%",
        end: "0% 90%",
        onEnter: () => $(this).addClass("inview")
      });
    });
}