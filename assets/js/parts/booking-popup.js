var bookingPopupOpen = false;
var currentRoomId = null;

// Room ID mapping
const roomMapping = {
    'boho': 67441,
    'deco': 67440,
    'retro': 67439,
    'indo': 67436
};

$(document).ready(function(){
    $(document).on("initPage", function(){
        initBookingPopup();
        checkForRoomBooking();
    });

    // Handle hash changes for direct room links
    $(window).on('hashchange', function() {
        checkForRoomBooking();
    });
});

function initBookingPopup() {
    const popup = $('#bookingPopup');
    
    if (popup.length === 0) {
        return;
    }

    // Remove existing event listeners to prevent duplicates
    $(document).off('click.bookingPopup');
    
    // Open popup when clicking button with data-book attribute
    $(document).on('click.bookingPopup', '[data-book]', function(e) {
        e.preventDefault();
        openBookingPopup();
    });

    // <PERSON>le clicks on links with #book hash
    $(document).on('click.bookingPopup', 'a[href^="#book"]', function(e) {
        e.preventDefault();
        const href = $(this).attr('href');

        console.log('Room booking link clicked:', href);

        // Update URL hash
        window.location.hash = href;

        // Trigger room booking check
        checkForRoomBooking();
    });

    // Close popup when clicking close button or overlay
    $(document).on('click.bookingPopup', '#bookingPopup .popupClose, #bookingPopup .popupOverlay', function(e) {
        e.preventDefault();
        closeBookingPopup();
    });

    // Close popup when pressing ESC key
    $(document).on('keydown.bookingPopup', function(e) {
        if (e.key === 'Escape' && bookingPopupOpen) {
            closeBookingPopup();
        }
    });

    // Tab switching functionality
    $(document).on('click.bookingPopup', '#bookingPopup .tabButton', function(e) {
        e.preventDefault();
        const tabName = $(this).data('tab');
        switchTab(tabName);
    });
}

function checkForRoomBooking() {
    const hash = window.location.hash;

    // Check for #book?room=67441 or #book?room=boho format
    if (hash.startsWith('#book')) {
        const urlParams = new URLSearchParams(hash.substring(hash.indexOf('?') + 1));
        const roomParam = urlParams.get('room');

        if (roomParam) {
            let roomId = null;

            // Check if it's a numeric ID or room name
            if (isNaN(roomParam)) {
                // It's a room name, convert to ID
                roomId = roomMapping[roomParam.toLowerCase()];
            } else {
                // It's already a numeric ID
                roomId = parseInt(roomParam);
            }

            if (roomId) {
                currentRoomId = roomId;
                openBookingPopup('rooms');
                return;
            }
        }

        // If just #book without room parameter, open popup normally
        openBookingPopup();
    }
}

function openBookingPopup(defaultTab = 'restaurant') {
    const popup = $('#bookingPopup');
    
    if (popup.length === 0) {
        return;
    }

    bookingPopupOpen = true;
    popup.addClass('active');

    // Stop the scroller when popup is open
    if (typeof scroller !== 'undefined') {
        scroller.stop();
    }

    // Load specified tab
    switchTab(defaultTab);
}

function closeBookingPopup() {
    const popup = $('#bookingPopup');
    
    if (popup.length === 0) {
        return;
    }

    bookingPopupOpen = false;
    popup.removeClass('active');

    // Restart the scroller when popup is closed
    if (typeof scroller !== 'undefined') {
        scroller.start();
    }

    // Reset popup state
    resetBookingPopup();
}

function switchTab(tabName) {
    const popup = $('#bookingPopup');
    
    // Update tab buttons
    popup.find('.tabButton').removeClass('active');
    popup.find('.tabButton[data-tab="' + tabName + '"]').addClass('active');
    
    // Update tab panes
    popup.find('.tabPane').removeClass('active');
    popup.find('#' + tabName + '-tab').addClass('active');
    
    // Load content based on tab
    if (tabName === 'restaurant') {
        loadRestaurantBooking();
    } else if (tabName === 'rooms') {
        loadRoomsBooking();
    }
}

function loadRestaurantBooking() {
    const container = $('#restaurant-embed');
    const loadingMessage = $('#restaurant-tab .loadingMessage');
    
    // Show loading message
    loadingMessage.show();
    container.hide();
    
    // TODO: Replace with actual OpenTable embed when available
    // For now, show a placeholder message
    setTimeout(function() {
        loadingMessage.hide();
        container.html('<div style="padding: 40px; text-align: center; color: #666;"><p>OpenTable integration will be added here.<br>Please provide the OpenTable embed code.</p></div>');
        container.show();
    }, 1000);
}

function loadRoomsBooking() {
    const container = $('#rooms-embed');
    const loadingMessage = $('#rooms-tab .loadingMessage');

    // Show loading message
    loadingMessage.show();
    container.hide();

    // console.log('Starting InnStyle iframe loading...');

    // Try to load the InnStyle booking system in an iframe
    try {
        // Create iframe with minimal restrictions and optimized settings
        const iframe = document.createElement('iframe');
        iframe.style.width = '100%';
        iframe.style.height = '600px';
        iframe.style.border = 'none';
        iframe.style.borderRadius = '8px';
        iframe.style.background = '#fff';
        iframe.style.minHeight = '600px';

        // Use minimal sandbox for maximum compatibility
        iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation allow-downloads allow-modals');
        iframe.setAttribute('loading', 'eager');
        iframe.setAttribute('allow', 'payment; geolocation');
        iframe.setAttribute('referrerpolicy', 'no-referrer-when-downgrade');

        // Set the source to InnStyle booking with optional room parameter
        let iframeSrc = 'https://thefig.innstyle.co.uk/';
        if (currentRoomId) {
            // Add room parameter to URL for direct room booking
            iframeSrc += '?bookable=' + currentRoomId;
        }
        iframe.src = iframeSrc;

        let iframeLoaded = false;

        // Handle successful loading
        iframe.onload = function() {
            // console.log('InnStyle iframe loaded successfully');
            iframeLoaded = true;
            loadingMessage.hide();
            container.show();

            // Try to hide logo and unnecessary elements via CSS injection
            try {
                setTimeout(function() {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        // Inject CSS to hide logo and clean up interface
                        const style = iframeDoc.createElement('style');
                        style.textContent = `
                            .logo, .header-logo, .site-logo, .brand-logo,
                            .navbar-brand, .header-brand, .top-logo,
                            [class*="logo"], [id*="logo"],
                            .header-top, .site-header-top,
                            .promotional-banner, .promo-banner,
                            .advertisement, .ads, .social-links,
                            .footer-links, .footer-social {
                                display: none !important;
                                visibility: hidden !important;
                            }
                            body {
                                padding-top: 0 !important;
                                margin-top: 0 !important;
                            }
                            .main-content, .booking-content {
                                padding-top: 20px !important;
                            }
                        `;
                        iframeDoc.head.appendChild(style);
                    }
                }, 1000);
            } catch (e) {
                // Cross-origin restrictions prevent CSS injection, which is expected
                console.log('CSS injection blocked by CORS (expected)');
            }

            // Add a small note about CSP warnings being normal
            if (console && console.info) {
                console.info('ℹ️ InnStyle booking system loaded. Any CSP warnings from thefig.innstyle.co.uk are normal and don\'t affect functionality.');
            }
        };

        // Handle loading errors
        iframe.onerror = function(e) {
            console.error('InnStyle iframe failed to load:', e);
            if (!iframeLoaded) {
                showRoomsFallback();
            }
        };

        // Clear container and add iframe
        container.empty();
        container.append(iframe);

        // Extended timeout - give iframe more time to load
        setTimeout(function() {
            if (loadingMessage.is(':visible') && !iframeLoaded) {
                console.log('InnStyle iframe loading timeout after 15 seconds, showing fallback');
                showRoomsFallback();
            }
        }, 15000); // 15 second timeout

        // Also try to detect if iframe content loads
        setTimeout(function() {
            try {
                if (iframe.contentDocument || iframe.contentWindow) {
                    console.log('InnStyle iframe content detected');
                    if (!iframeLoaded) {
                        iframeLoaded = true;
                        loadingMessage.hide();
                        container.show();
                    }
                }
            } catch (e) {
                // Cross-origin, but that's expected
                console.log('InnStyle iframe cross-origin (expected)');
                if (!iframeLoaded) {
                    iframeLoaded = true;
                    loadingMessage.hide();
                    container.show();
                }
            }
        }, 3000); // Check after 3 seconds

    } catch (error) {
        console.error('Error creating InnStyle iframe:', error);
        showRoomsFallback();
    }
}

function initInnStyle() {
    // This function is no longer needed with iframe approach
    // but keeping it for backwards compatibility
}

function showRoomsError() {
    const container = $('#rooms-embed');
    const loadingMessage = $('#rooms-tab .loadingMessage');

    loadingMessage.hide();
    container.html('<div style="padding: 40px; text-align: center; color: #E55D2D;"><p>Error loading room booking system.<br>Please try again later.</p></div>');
    container.show();
}

function showRoomsFallback() {
    const container = $('#rooms-embed');
    const loadingMessage = $('#rooms-tab .loadingMessage');

    // Build fallback URL with room parameter if available
    let fallbackUrl = 'https://thefig.innstyle.co.uk/';
    if (currentRoomId) {
        fallbackUrl += '?bookable=' + currentRoomId;
    }

    loadingMessage.hide();
    container.html(`
        <div style="padding: 40px; text-align: center; background: #F5F1EA; border-radius: 12px; margin: 20px;">
            <div style="margin-bottom: 20px;">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" style="margin-bottom: 15px;">
                    <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#E55D2D" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </div>
            <h3 style="margin-bottom: 15px; color: #232020; font-size: 20px;">Booking System Unavailable</h3>
            <p style="margin-bottom: 25px; color: #666; font-size: 14px; line-height: 1.5;">The booking system couldn't load properly.<br>Please book directly through our external booking page.</p>
            <a href="${fallbackUrl}" target="_blank" style="display: inline-block; padding: 12px 30px; background: #E55D2D; color: white; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 14px; text-transform: uppercase; letter-spacing: 1px; transition: all 0.3s ease;" onmouseover="this.style.background='#d14a26'" onmouseout="this.style.background='#E55D2D'">
                Book Externally →
            </a>
            <p style="margin-top: 15px; font-size: 12px; color: #999;">Opens in new window</p>
        </div>
    `);
    container.show();
}

function resetBookingPopup() {
    const popup = $('#bookingPopup');

    // Reset room ID
    currentRoomId = null;

    // Reset to default tab (restaurant)
    popup.find('.tabButton').removeClass('active');
    popup.find('.tabButton[data-tab="restaurant"]').addClass('active');

    popup.find('.tabPane').removeClass('active');
    popup.find('#restaurant-tab').addClass('active');

    // Clear embed containers and remove any iframes
    popup.find('.embedContainer').empty();
    popup.find('.loadingMessage').show();

    // Remove any dynamically loaded InnStyle scripts or iframes
    $('script[src*="innstyle"]').not('#InnStyle-js').remove();
    popup.find('iframe').remove();

    // Clear URL hash if it contains booking parameters
    if (window.location.hash.startsWith('#book')) {
        history.replaceState(null, null, window.location.pathname + window.location.search);
    }
}
