var parallaxLoadOffset = 1000;
var parallaxIndicator = 20;

$(document).ready(function() {

  // Parallax initialiseren bij elke nieuwe pagina (na Swup reload)
  $(document).on("initPage", function() {
    if ($(window).width() < $(window).height()) {
      parallaxIndicator = 30;
    }

    // Eerst alle bestaande triggers killen om dubbele animaties te voorkomen
    ScrollTrigger.getAll().forEach((t) => t.kill());

    // Wacht even tot Lenis en DOM klaar zijn
    setTimeout(() => {
      changeParallaxElements();
      ScrollTrigger.refresh();
    }, 100);
  });

});

function changeParallaxElements() {
  $("[data-parallax]").each(function (i, el) {
    const $el = $(el);
    const speed = parseFloat($el.data("parallax-speed")) || 1;
    const direction = $el.data("scroll-direction") || "vertical";
    const positionType = $el.data("scroll-position") || "center";

    ScrollTrigger.create({
      trigger: el,
      start: "top bottom",
      end: "bottom top",
      onUpdate: (self) => {
        if (!self.isActive) return;

        // ✅ Gebruik de actuele Lenis scrollwaarde als die er is
        let scrollY = typeof currentScrollY !== "undefined" ? currentScrollY : window.scrollY;
        let pos;

        if (positionType === "top") {
          const factor = scrollY / (parallaxIndicator * 2);
          pos = (factor * speed) * (factor < 0 ? -1 : 1);
        } else {
          pos =
            (($el.offset().top - scrollY - (($(window).height() / 2) - ($el.height() / 2))) /
              parallaxIndicator) * speed;
        }

        // Beweeg horizontaal of verticaal
        if (direction === "horizontal") {
          gsap.to(el, { x: pos, duration: 0, force3D: true, overwrite: true });
        } else {
          gsap.to(el, { y: pos, duration: 0, force3D: true, overwrite: true });
        }
      },
    });
  });
}
