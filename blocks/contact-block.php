<?php
$image = get_field("image");
$size = 'large';
$video = get_field("video");
 ?>
<section class="contactBlock" data-init>
  <div class="backgroundImage">
    <div class="innerImage">
      <?php if ($video): ?>
        <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
          <source src="<?php echo esc_url($video); ?>" type="video/mp4">
        </video>
      <?php elseif( $image ): ?>
        <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
      <?php endif; ?>
    </div>
    <div class="titleWrapper">
      <div class="contentWrapper">
        <h1 class="hugeTitle white" data-init data-split><?php the_field("title") ?></h1>
      </div>
    </div>
  </div>
  <div class="contentWrapper small">
    <div class="cols">
      <div class="col">
        <div class="innerCol">
          <h3 class="title white"><?php the_field("location_title") ?></h3>
          <div class="text white">
            <p>
              <?php the_field("location_text") ?>
            </p>
          </div>
        </div>
        <div class="innerCol">
          <h3 class="title white"><?php the_field("hours_title") ?></h3>
          <div class="text white">
            <p>
              <?php the_field("hours_text") ?>
            </p>
          </div>
        </div>
        <div class="innerCol">
          <!--  tripadvisor travvelers choice awards 2024  -->
            <?php if (get_field("tripadvisor")): ?>
            <div class="tripadvisor">

            </div>
            <?php endif; ?>
          <?php if (get_theme_mod('customTheme-main-callout-tripadvisor')) : ?>
            <a class="tripAdvisor" href="<?php echo esc_html(get_theme_mod('customTheme-main-callout-tripadvisor')); ?>" target="_blank">
              <img src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-tripadvisor-image'))); ?>" alt="Tripadvisor" />
            </a>
          <?php endif; ?>
          <div class="socials">
            <?php include("parts/socials.php"); ?>
          </div>
          <div class="text white">
            <p>
              Website by <a class="link" href="https://www.bforbroady.com/" target="_blank">BforBroady</a>
            </p>
          </div>
        </div>
      </div>
      <div class="col">
        <h3 class="title white">Let’s Talk</h3>
        <div class="formWrapper">
          <?php echo do_shortcode(get_field('form')); ?>
        </div>
      </div>
    </div>
  </div>
</section>
