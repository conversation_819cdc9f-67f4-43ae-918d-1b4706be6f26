<?php
$size = 'full';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="bigHeaderBlock" data-init>
  <div class="backgroundImage">
    <div class="innerImage">
      <div class="innerTransform">
        <img class="lazy" data-src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
      </div>
    </div>
    <div class="titleWrapper">
      <div class="contentWrapper">
        <h1 class="hugeTitle splitThis white" data-init data-split><?php the_field("title") ?></h1>
      </div>
    </div>
  </div>
</section>