<?php
$size = 'large';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = str_replace(['<p>', '</p>'], '', get_field("quote"));
?>

<section class="quoteImageBlock <?php the_field("background") ?><?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper <?php if (get_field("image_right")){ ?>smaller<?php } ?>">
    <?php if (!get_field("image_right")) { ?>
      <div class="col image">
        <div class="imageWrapper">
          <div class="innerImage">
            <img class="lazy" alt="<?php echo esc_attr($image['alt']); ?>" data-src="<?php echo esc_url($image['sizes'][$size]); ?>"/>
          </div>
        </div>
      </div>
    <?php } ?>
    <div class="col" data-parallax data-parallax-speed="2">
      <div class="innerCol">
        <h2 class="bigTitle splitThis" data-init data-split><?php echo $title; ?></h2>
      </div>
      <?php if (get_field("subtext")) { ?>
      <div class="innerCol">
        <h3 class="normalTitle"><?php the_field("subtext") ?></h3>
      </div>
      <?php } ?>
      <div class="innerCol">
        <h4 class="signature primary"><?php the_field("signature") ?></h4>
      </div>
    </div>
    <?php if (get_field("image_right")) { ?>
      <div class="col image">
        <div class="imageWrapper">
          <div class="innerImage">
            <img class="lazy" alt="<?php echo esc_attr($image['alt']); ?>" data-src="<?php echo esc_url($image['sizes'][$size]); ?>"/>
          </div>
        </div>
      </div>
    <?php } ?>
  </div>
</section>
