<?php
$size = 'full';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = get_field("title");
?>

<section class="roomsMarqueeBlock" data-init>
  <?php $rooms = get_posts(array(
    'post_type' => 'rooms',
    'posts_per_page' => -1,
    'orderby' => 'menu_order',
    'order' => 'ASC'
  ));
  if ($rooms) : ?>
    <div class="backgrounds">
      <?php $counter = 0; ?>
      <?php foreach ($rooms as $room) : ?>
        <div class="background" data-room="<?php echo get_the_title($room->ID); ?>" data-index="<?php echo $counter; ?>">
          <!-- get field images and get the first image from gellery: -->
           <?php $images = get_field('room_gallery', $room->ID); ?>
          <?php if ($images) : ?>
            <img src="<?php echo esc_url($images[0]['url']); ?>" alt="<?php echo esc_attr($images[0]['alt']); ?>" />
          <?php endif; ?>
        </div>
        <?php $counter++; ?>
      <?php endforeach; ?>
    </div>
    <div class="sliderIndicator">
      <span class="arrow prev"><i class="icon-arrow-left"></i></span>
      <span class="current"></span>
      <span class="indicator"><span class="innerBar"></span></span>
      <span class="total"><?php echo count($rooms); ?></span>
      <span class="arrow next"><i class="icon-arrow-right"></i></span>
     </div>
    <div class="marquee" data-marquee data-marquee-direction="right" data-marquee-speed="30" data-marquee-scroll-speed="5" data-marquee-swipe="true">
          <div class="marqueeScroll">    
              <div class="itemsContainer">
                  <div class="items">
                      <?php $counter = 0; ?>
                      <?php foreach ($rooms as $room) : ?>
                          <?php
                          $room_slug = get_post_field('post_name', $room->ID);
                          $rooms_page_url = '/rooms/'; // Adjust this to your actual rooms page URL
                          ?>
                          <div class="item" data-index="<?php echo $counter; ?>">
                              <a href="<?php echo esc_url($rooms_page_url . '#' . $room_slug); ?>" class="hashtagLink">
                                  <h3 class="hugeTitle"><?php echo get_the_title($room->ID); ?></h3>
                              </a>
                          </div>
                          <?php $counter++; ?>
                      <?php endforeach; ?>
                  </div>
          </div>
      </div>
    </div>
    <!-- text link -->
     <?php if (get_field("link")):
      $link = get_field("link");
      if( $link ) {
          $link_url = $link['url'];
          $link_title = $link['title'];
          $link_target = $link['target'] ? $link['target'] : '_self';
      }
      ?>
    <?php include("parts/textlink.php"); 
    endif; ?>
  <?php endif; ?>
  
</section>
