<?php
$size = 'large'; // (thumbnail, medium, large, full or custom size)
$members = get_posts(array(
    'posts_per_page' => 6,
    'order' => 'ASC',
    'post_type'     => 'member',
));
$the_query = new WP_Query( $members );
$title = str_replace(['<p>', '</p>'], '', get_field("title"));
?>
<section class="staffSliderBlock black<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper smaller">
    <div class="wrapper">
      <div class="innerContent">
        <h2 class="bigTitle splitThis" data-init data-split><?php echo $title; ?></h2>
        <div class="text grey">
          <?php the_field("text") ?>
        </div>
        <?php
            $link = get_field("link");
            if( $link ) {
                $mobile = true;
                $link_url = $link['url'];
                $link_title = $link['title'];
                $link_target = $link['target'] ? $link['target'] : '_self';
                include("parts/round_button.php");
            }
          ?>
      </div>
    </div>
    <div class="sliderWrapper">
      <div class="slider" data-slider>
        <?php if( $members ): ?>
            <?php foreach( $members as $post ): setup_postdata($post);
              $title = get_the_title($post->ID);
              $image = get_field( 'image', $post->ID );
              $function = get_field( 'function', $post->ID );
              ?>
              <div class="slide">
                <div class="imageWrapper">
                  <div class="innerImage">
                    <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
                  </div>
                </div>
                <h3 class="subTitle"><span class="primary"><?php echo esc_html( $title ); ?></span> | <?php echo esc_html( $function ); ?></h3>
              </div>
            <?php endforeach; ?>
            <?php wp_reset_postdata(); ?>
        <?php endif; ?>
      </div>
      <div class="sliderButton prev" data-prev><?php include("parts/slider_button.php");  ?></div>
      <div class="sliderButton next" data-next><?php include("parts/slider_button.php");  ?></div>
      <div class="sliderIndicator"><div class="innerBar"></div></div>
    </div>
  </div>
</section>