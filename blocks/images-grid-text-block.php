<?php
$size = 'medium'; // (thumbnail, medium, large, full or custom size)
$logo = get_field("logo");
$image1 = get_field("image_1");
$image2 = get_field("image_2");
$image3 = get_field("image_3");
$image4 = get_field("image_4");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$video = get_field("video_url");
?>

<section class="imagesGridTextBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init data-init-delay=300>
  <div class="contentWrapper">
    <div class="cols">
      <div class="col top">
        <div class="images">
        <?php
          $images = [$image1, $image2, $image3, $image4];
          $delay = 300;
          $delayIncrement = 100;

          foreach ($images as $index => $image) {
              // Bepaal de grootte per afbeelding
              $size = ($index === 0 || $index === 3) ? 'large' : 'medium'; // Pas de size aan voor image1 (index 0) en image4 (index 3)
              ?>
              <div class="imageItem" data-init data-init-delay="<?php echo $delay; ?>">
                  <div class="innerImage">
                      <div class="innerMask">
                          <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
                      </div>
                  </div>
              </div>
              <?php
              $delay += $delayIncrement;
          }
          ?>
        </div>
      </div>
      <div class="col">
        <div class="textWrapper">
          <div class="subTitle primary"><?php the_field("subtitle") ?></div>
          <h2 class="bigTitle splitThis" data-init data-split><?php the_field("title") ?></h2>
          <div class="text">
              <?php the_field("text") ?>
              <?php
              $link = get_field("link");
              if( $link ) {
                  $link_url = $link['url'];
                  $link_title = $link['title'];
                  $link_target = $link['target'] ? $link['target'] : '_self';

               ?>
               <div class="buttonWrapper">
                 <?php include("parts/textlink.php");  ?>
               </div>
            <?php } ?>

            <?php
              $link = get_field("link-2");
              if( $link ) {
                  $link_url = $link['url'];
                  $link_title = $link['title'];
                  $link_target = $link['target'] ? $link['target'] : '_self';

               ?>
               <div class="buttonWrapper">
                 <?php include("parts/textlink.php");  ?>
               </div>
            <?php } ?>

          </div>
        </div>
      </div>
    </div>
  </div>
</section>
