<?php
$size = 'medium_large';
$items = get_field('items');
?>

<section class="fourImagesTextBlock" data-init>
  <div class="contentWrapper smaller">
    <div class="titleWrapper">
      <h2 class="title splitThis" data-lines data-words><?php the_field("title") ?></h2>
      <div class="text">
        <p><?php the_field("text") ?></p>
      </div>
    </div>
  </div>
  <div class="contentWrapper">
    <div class="cols">
        <?php $counter = 0; ?>
      <?php foreach ($items as $item): ?>
      <div class="col">
        <div class="item" data-parallax data-parallax-speed="<?php echo ($counter * 0.5) + 1; ?>">
            <div class="imageWrapper">
              <div class="innerImage">
                 <?php $image = $item['image']; ?>
                <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
              </div>
            </div>
            <h3 class="smallerTitle white"><?php echo $item['title']; ?></h3>
        </div>
      </div>
      <?php $counter++; ?>
      <?php endforeach; ?>
    </div>
  </div>
</section>