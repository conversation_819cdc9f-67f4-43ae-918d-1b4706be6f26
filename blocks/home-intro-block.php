<?php
  $size = 'medium_large'; // (thumbnail, medium, large, full or custom size)
  $logo = get_field("logo");
  $image1 = get_field("image_1");
  $image2 = get_field("image_2");
  $image3 = get_field("image_3");
  $image4 = get_field("image_4");
  $text1 = get_field('text_1');
  $text2 = get_field('text_2');
  $pattern = '/(.*)\*(.*)\*(.*)/';
  $replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="homeIntroBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init data-init-delay=300>
  <div class="contentWrapper">
    <div class="cols">
      <div class="col small">
        <div class="imageItem" data-init data-init-delay=300>
          <div class="innerImage">
            <div class="innerMask">
              <img class="lazy" data-src="<?php echo esc_url($image1['sizes'][$size]); ?>" alt="<?php echo esc_attr($image1['alt']); ?>"/>
            </div>
          </div>
        </div>
        <div class="imageItem" data-init data-init-delay=300>
          <div class="innerImage">
              <div class="innerMask">
              <img class="lazy" data-src="<?php echo esc_url($image2['sizes'][$size]); ?>" alt="<?php echo esc_attr($image2['alt']); ?>"/>
              </div>
          </div>
      </div>
      </div>
      <div class="col big">
        <h2 class="bigTitle splitThis" data-init data-split><?php the_field("text_1") ?></h2>
        <h2 class="bigTitle splitThis" data-init data-split><?php the_field("text_2") ?></h2>
        <div class="subTitleWrapper" data-init data-init-delay=600><h3 class="subTitle primary"><?php the_field("subtitle") ?></h3><i class="icon-bolwerk"></i></div>
        <div class="signature" data-init data-init-delay=900><?php the_field("signature") ?></div>
        <?php
          $link = get_field("link");
          if( $link ) {
              $mobile = true;
              $link_url = $link['url'];
              $link_title = $link['title'];
              $link_target = $link['target'] ? $link['target'] : '_self';
              include("parts/round_button.php");
          }
        ?>
      </div>
      <div class="col small">
        <div class="imageItem" data-init data-init-delay=300>
            <div class="innerImage">
                <div class="innerMask">
                  <img class="lazy" data-src="<?php echo esc_url($image3['sizes'][$size]); ?>" alt="<?php echo esc_attr($image3['alt']); ?>"/>
                </div>
            </div>
        </div>
        <?php
          $link = get_field("link");
          if( $link ) {
              $mobile = false;
              $link_url = $link['url'];
              $link_title = $link['title'];
              $link_target = $link['target'] ? $link['target'] : '_self';
              include("parts/round_button.php");
          }
        ?>
        <div class="imageItem" data-init data-init-delay=300>
            <div class="innerImage">
                <div class="innerMask">
                <img class="lazy" data-src="<?php echo esc_url($image4['sizes'][$size]); ?>" alt="<?php echo esc_attr($image4['alt']); ?>"/>
                </div>
            </div>
        </div>
      </div>
    </div>
  </div>
</section>
