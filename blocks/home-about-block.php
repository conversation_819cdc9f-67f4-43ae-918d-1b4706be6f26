<?php
$size = 'thumbnail'; // (thumbnail, medium, large, full or custom size)
$logo = get_field("logo");
$image1 = get_field("image_top");
$image2 = get_field("image_bottom");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$video = get_field("video_url");
?>

<section class="homeAboutBlock dark<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init data-init-delay=300>
  <div class="contentWrapper">
    <div class="cols">
      <div class="col top">
        <div class="textWrapper">
          <div class="subTitle primary"><?php the_field("subtitle") ?></div>
          <h2 class="bigTitle white splitThis" data-init data-split><?php the_field("title") ?></h2>
        </div>
        <div class="imageWrapper">
          <div class="innerImage">
            <img class="lazy" data-src="<?php echo esc_url($image1["sizes"]['large']); ?>" alt="<?php echo esc_attr($image1['alt']); ?>" />
          </div>
        </div>
      </div>

      <div class="col bottom">
        <div class="imageWrapper">
          <div class="innerImage">
            <?php if ($video): ?>
              <video poster="<?php echo esc_url($image2['url']); ?>" class="video" muted playsinline loop autoplay>
                <source src="<?php echo esc_url($video); ?>" type="video/mp4">
              </video>
            <?php elseif( $image2 ): ?>
              <img class="lazy" data-src="<?php echo esc_url($image2["sizes"]['large']); ?>" alt="<?php echo esc_attr($image2['alt']); ?>" />
            <?php endif; ?>
          </div>
        </div>

        <div class="textWrapper">
          <div class="text grey">
            <?php the_field("text"); ?>
            <?php
              $link = get_field("link");
              if( $link ):
                $link_url = $link['url'];
                $link_title = $link['title'];
                $link_target = $link['target'] ? $link['target'] : '_self';
            ?>
              <div class="buttonWrapper">
                <?php include("parts/textlink.php"); ?>
              </div>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
