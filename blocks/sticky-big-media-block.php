<?php
$images = get_field('images');
$opposite = get_field('opposite');
$video = get_field('video');
?>
<section class="stickyBigMediaBlock" data-init data-opposite="<?= $opposite ? '1':'0' ?>">
  <div class="contentWrapper small">
    <?php if ($video): ?>
      <div class="mediaWrapper">
        <div class="innerImage">
          <video autoplay muted loop playsinline>
            <source src="<?= esc_url($video['url']) ?>" type="video/mp4">
          </video>
        </div>
      </div>
    <?php else: ?>
      <?php if ($images): ?>
        <?php $counter = 0; ?>
        <div class="mediaWrapper">
          <div class="innerImage">
              <?php foreach ($images as $img): ?>
                <div class="item <?php if ($counter == 0) echo 'active'; ?>">
                  <img class="lazy" data-src="<?= esc_url($img['url']) ?>" alt="<?= esc_attr($img['alt'] ?? '') ?>">
                </div>
                <?php $counter++; ?>
              <?php endforeach; ?>
          </div>
        </div>
      <?php endif; ?>
    <?php endif; ?>
  </div>
</section>
