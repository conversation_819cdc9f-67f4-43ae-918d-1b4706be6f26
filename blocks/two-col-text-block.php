<?php
$size = 'full';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = str_replace(['<p>', '</p>'], '', get_field("title"));
?>

<section class="twoColTextBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper smaller">
    <div class="subTitle primary"><?php the_field("subtitle") ?></div>
    <h2 class="bigTitle splitThis" data-init data-split><?php echo $title; ?></h2>
    <div class="cols">
      <div class="col">
        <div class="text">
          <?php the_field("left_text") ?>
        </div>
      </div>
      <div class="col">
        <div class="text">
          <?php the_field("right_text") ?>
        </div>
      </div>
    </div>
  </div>
</section>
