$(document).ready(function(){
    $(document).on("initPage", function(){
        if ($(".contactBlock").length > 0) {
            initializeContactBlock();
        }
    });
  });

  function initializeContactBlock() {
    var form = $(".contactBlock form")[0];
    if (form) {
        form.reset();
    }

    gsap.to(".contactBlock .innerImage", {
        yPercent: 50,
        scrollTrigger: {
        trigger: ".contactBlock",
        start: "top top",
        end: "bottom top",
        scrub: true,
        },
    });

    // Configuratie voor de datepicker
    const datePickerConfig = {
        minDate: "today",
        disable: [
            function(date) {
                // Maandag (1) en Dinsdag (2) blokkeren
                return (date.getDay() === 1 || date.getDay() === 2);
            },
            "2025-05-11",
            "2025-05-25", // Toegevoegd: afsluiting voetbal
            "2025-05-14",
            { from: "2025-08-02", to: "2025-08-21" }, // Vakantie
            { from: "2025-09-10", to: "2025-09-18" },  // Kermis
            { from: "2025-12-31", to: "2026-01-14" }
        ],
        dateFormat: "Y-m-d, H:i",
        disableMobile: true, // Zet op true om flatpickr ook op mobiel te gebruiken
        locale: {
            firstDayOfWeek: 1
        },
        // Extra opties voor betere mobiele ondersteuning
        allowInput: false, // Voorkomt handmatige invoer
        time_24hr: true,
        enableTime: true,
        minuteIncrement: 30,
        // Zorg ervoor dat de gebruiker geen geblokkeerde datums kan selecteren
        onDayCreate: function(_, __, ___, dayElem) {
            if (dayElem.classList.contains('flatpickr-disabled')) {
                // Voeg extra styling toe aan uitgeschakelde dagen
                dayElem.style.textDecoration = 'line-through';
                dayElem.style.color = '#ccc';
            }
        }
    };

    // Initialiseer flatpickr voor zowel desktop als mobiel
    $('.ff-el-datepicker').each(function() {
        const $input = $(this);
        const flatpickrInstance = $input.flatpickr(datePickerConfig);

        // Voeg extra event listener toe voor iOS
        $input.on('click', function() {
            // Forceer flatpickr om te openen, zelfs op iOS
            if (flatpickrInstance && typeof flatpickrInstance.open === 'function') {
                flatpickrInstance.open();
            }
        });
    });
  }

  function formatDateNL(dateStr) {
      const date = new Date(dateStr);
      return date.toLocaleString('nl-NL', {
          weekday: 'long',
          day: 'numeric',
          month: 'long',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
      });
  }
