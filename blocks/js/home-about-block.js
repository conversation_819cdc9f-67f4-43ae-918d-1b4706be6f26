$(document).ready(function(){

  $(document).on("initPage", function(){
    initializeHomeAboutBlock();
  });
});

function initializeHomeAboutBlock() {
  if ($(".homeAboutBlock").length > 0) {
    setTimeout(function() {
      setHomeAboutBlock();
    }, 300);
  }
}

function setHomeAboutBlock() {
  var $textWrapper = $('.homeAboutBlock .bottom .text');
  var $imageWrapper = $('.homeAboutBlock .bottom .textWrapper');

  var originalOffsetTop = $textWrapper.offset().top;
  var wrapperHeight = $imageWrapper.outerHeight();
  var textWrapperHeight = $textWrapper.outerHeight();

  scroller.on('scroll', function() {
    var scrollTop = $(window).scrollTop();
    var stickyStart = originalOffsetTop;
    var stickyEnd = stickyStart + wrapperHeight - textWrapperHeight;

    if (scrollTop >= stickyStart && scrollTop <= stickyEnd) {
      $textWrapper.css({
        top: (scrollTop - stickyStart) + 'px'
      });
    } else if (scrollTop > stickyEnd) {
      $textWrapper.css({
        top: (wrapperHeight - textWrapperHeight) + 'px'
      });
    } else {
      $textWrapper.css({
        top: '0px'
      });
    }
  });
}
