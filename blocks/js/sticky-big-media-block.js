$(document).ready(function() {
    $(document).on("initPage", function() {
        if ($(".stickyBigMediaBlock").length > 0) {
            initStickyBigMedia();
        }
    });
});

function initStickyBigMedia() { 
    var isMobile = $(window).outerWidth() <= 580;
    $(".stickyBigMediaBlock").each(function(i, el){
        var stickyElement = $(el).find(".mediaWrapper");
        var sizeCalculation = stickyElement.find(".item").length == 1 ? 3 : 5;
        if (isMobile) {
            sizeCalculation = stickyElement.find(".item").length / 2;
        }
        let settings = isMobile ? 0.0001 : 0.1;
        $(el).css("height", $(el).outerHeight() * sizeCalculation);
        setTimeout(function() {
            gsap.to(stickyElement, settings, {
                width: 100 + "%",
                scrollTrigger: {
                    start: "center center",
                    end: "top+=" + ($(el).outerHeight() - $('header').outerHeight()) + " top",
                    trigger: stickyElement,
                    scrub: true,
                    onUpdate(self) {
                        var items = stickyElement.find(".item");
                        if (items.length == 1) return;
                        var activeItem = Math.floor((items.length ) * self.progress);
                        items.removeClass("active");
                        items.eq(activeItem).addClass("active");
                    }
                }
            });
        }, 100);
        
    });
    if (!isMobile) {
        var $textWrapper = $('.stickyBigMediaBlock .mediaWrapper');
        var $imageWrapper = $('.stickyBigMediaBlock .contentWrapper');
        var wrapperHeight = $imageWrapper.outerHeight();
        var originalOffsetTop = $textWrapper.offset().top;
        var textWrapperHeight = $textWrapper.outerHeight();

        scroller.on('scroll', function() {
            textWrapperHeight = $textWrapper.outerHeight();
            var scrollTop = $(window).scrollTop() + ($("header").outerHeight() * 1.5);
            var stickyStart = originalOffsetTop;
            var stickyEnd = stickyStart + wrapperHeight - textWrapperHeight;

            if (scrollTop >= stickyStart && scrollTop <= stickyEnd) {
            $textWrapper.css({
                top: (scrollTop - stickyStart) + 'px'
            });
            } else if (scrollTop > stickyEnd) {
            $textWrapper.css({
                top: (wrapperHeight - textWrapperHeight) + 'px'
            });
            } else {
            $textWrapper.css({
                top: '0px'
            });
            }
        });
    }
}