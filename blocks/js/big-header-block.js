$(document).ready(function(){

  $(document).on("initPage", function(){
    if ($(".bigHeaderBlock").length > 0) {
      initializeBigHeaderBlock();
    }
  });
});

function initializeBigHeaderBlock() {
  $(".bigHeaderBlock").each(function(i, el) {
    if (i === 0) {
      gsap.to($(el).find(".innerTransform"), {
        // yPercent: 30,
        scale: 1.2,
        scrollTrigger: {
          trigger: $(el),
          start: "top top",
          end: "bottom top",
          scrub: true,
        },
      });
    } else {
      gsap.to($(el).find(".innerTransform"), {
        scale: 1.2,
        scrollTrigger: {
          trigger: $(el),
          start: "top bottom",
          end: "bottom top",
          scrub: true,
        },
      });
    }
    // gsap.to($(el).find(".innerTransform"), {
    //   yPercent: 50,
    //   scrollTrigger: {
    //     trigger: $(el),
    //     start: "top top",
    //     end: "bottom top",
    //     scrub: true,
    //   },
    // });
  });
}
