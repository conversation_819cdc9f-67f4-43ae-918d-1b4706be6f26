var curIndex;
var slideshowIntervals = [];

$(document).ready(function(){
  $(document).on("initPage", function(){
    initializeStickyDateTextBlock();
  });
});

function initializeStickyDateTextBlock() {
  if ($(".stickyDateTextBlock").length > 0) {
    if ($(window).outerWidth(true, true) > 580) {
      curIndex = 0;
      setStickyDateTextBlock();
    }
    initializeSlideshows();
  }
}

function setStickyDateTextBlock() {
  // SplitText initialisatie
  new SplitText(".stickyDateTextBlock .date", { type: "chars", charsClass: "letter" });
  var height = $(".stickyDateTextBlock .stickyItem").outerHeight(true, true);

  gsap.to(".stickyDateTextBlock .stickyItem", {
    scrollTrigger: {
      trigger: $(".stickyDateTextBlock .stickyItem").parents("section"),
      start: "top top",
      end: "bottom top+=" + height,
      scrub: true,
      onEnter: function() {
        $(".stickyDateTextBlock .stickyItem").css({
          top: 0,
          position: 'fixed'
        });
      },
      onEnterBack: function() {
        $(".stickyDateTextBlock .stickyItem").css({
          top: 0,
          position: 'fixed'
        });
      },
      onLeave: function() {
        $(".stickyDateTextBlock .stickyItem").css({
          top: ($(".stickyDateTextBlock .cols").outerHeight(true, true) - $(".stickyDateTextBlock .stickyItem").outerHeight(true, true)),
          position: 'absolute'
        });
      },
      onLeaveBack: function() {
        $(".stickyDateTextBlock .stickyItem").css({
          top: 0,
          position: 'absolute'
        });
      },
    }
  });

  $(".stickyDateTextBlock .dates .date").each(function(i, el) {
    if (i !== 0) {
      gsap.set($(el).find(".letter"), {
        yPercent: 100,
        opacity: 0
      });
    }
  });

  $(".stickyDateTextBlock .items .item").each(function(i, el) {
    var height = $(".stickyDateTextBlock .stickyItem").outerHeight(true, true);
    gsap.to(el, {
      scrollTrigger: {
        trigger: el,
        start: "top top+=" + height,
        end: "bottom top+=" + height,
        scrub: true,
        onEnter: function() {
          $(".stickyDateTextBlock .dates .date").eq(i).addClass("active");
          gsap.to($(".stickyDateTextBlock .dates .date").eq(i).find(".letter"), {
            yPercent: 0,
            opacity: 1,
            duration: 0.5,
            stagger: 0.03,
            ease: "power2.out"
          });
        },
        onEnterBack: function() {
          if (i !== ($(".stickyDateTextBlock .dates .date").length - 1)) {
            $(".stickyDateTextBlock .dates .date").eq(i).addClass("active");
            gsap.to($(".stickyDateTextBlock .dates .date").eq(i).find(".letter"), {
              yPercent: 0,
              opacity: 1,
              duration: 0.5,
              stagger: 0.03,
              ease: "power2.out"
            });
          }
        },
        onLeave: function() {
          if (i !== ($(".stickyDateTextBlock .dates .date").length - 1)) {
            setTimeout(function() {
              $(".stickyDateTextBlock .dates .date").eq(i).removeClass("active");
            }, 450);
          }
          gsap.to($(".stickyDateTextBlock .dates .date").eq(i).find(".letter"), {
            yPercent: -100,
            opacity: 0,
            duration: 0.4,
            stagger: 0.02,
            ease: "power2.in"
          });
        },
        onLeaveBack: function() {
          if (i !== 0) {
            setTimeout(function() {
              $(".stickyDateTextBlock .dates .date").eq(i).removeClass("active");
            }, 450);
            gsap.to($(".stickyDateTextBlock .dates .date").eq(i).find(".letter"), {
              yPercent: 100,
              opacity: 0,
              duration: 0.4,
              stagger: 0.02,
              ease: "power2.in"
            });
          }
        },
      }
    });
  });

  ScrollTrigger.refresh();
}

function initializeSlideshows() {
  // Clear any existing intervals
  slideshowIntervals.forEach(interval => clearInterval(interval));
  slideshowIntervals = [];

  // Initialize each slideshow
  $(".stickyDateTextBlock .slideshowWrapper").each(function() {
    const $slideshow = $(this);
    const $slides = $slideshow.find(".slideImage");
    const interval = parseInt($slideshow.data("interval")) * 1000 || 3000; // Default to 3 seconds

    if ($slides.length > 1) {
      // Start the slideshow
      const intervalId = setInterval(function() {
        const $currentSlide = $slideshow.find(".slideImage.active");
        const $nextSlide = $currentSlide.next(".slideImage").length ?
                          $currentSlide.next(".slideImage") :
                          $slides.first();

        // Fade out current slide
        $currentSlide.removeClass("active");

        // Fade in next slide
        $nextSlide.addClass("active");
      }, interval);

      // Store the interval ID so we can clear it later if needed
      slideshowIntervals.push(intervalId);
    }
  });
}
