$(document).ready(function(){
  $(document).on("initPage", function(){
    initializeMenuBlock();
  });
});

function initializeMenuBlock() {
  if ($(".menuBlock").length > 0) {
    setTimeout(function() {
      setMenuBlock();
    }, 300);
    $(".menuBlock").each(function(i, el) {
      $(el).find(".imageWrapper").attr("data-index", i);
    });
  }
}

function setMenuBlock() {
  if ($(window).outerWidth(true, true) > 580) {
    $('.menuBlock').each(function() {
      var $menuBlock = $(this);
      var $textWrapper = $menuBlock.find('.stickyElement');
      var $imageWrapper = $menuBlock.find('.stickyWrapper');

      var originalOffsetTop = $textWrapper.offset().top;
      var wrapperHeight = $imageWrapper.outerHeight();
      var textWrapperHeight = $textWrapper.outerHeight();

      $(window).on('scroll.menuBlock', function() {
        var scrollTop = $(window).scrollTop();
        var stickyStart = originalOffsetTop;
        var stickyEnd = stickyStart + wrapperHeight - textWrapperHeight;

        if (scrollTop >= stickyStart && scrollTop <= stickyEnd) {
          $textWrapper.css({
            top: (scrollTop - stickyStart) + 'px'
          });
        } else if (scrollTop > stickyEnd) {
          $textWrapper.css({
            top: (wrapperHeight - textWrapperHeight) + 'px'
          });
        } else {
          $textWrapper.css({
            top: '0px'
          });
        }
      });
    });
  }
}
