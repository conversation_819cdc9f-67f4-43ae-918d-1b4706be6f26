/**
 * PDF Menus Block JavaScript
 * Handles filtering, PDF viewing, and download functionality
 */

function initPdfMenusBlock(blockId) {
    const $block = $('#' + blockId);
    if (!$block.length) return;

    const $filterButtons = $block.find('.filterBtn');
    const $menuItems = $block.find('.menuItem');
    const $categoryMenus = $block.find('.categoryMenus');
    const $downloadBtn = $block.find('.downloadBtn');
    const $pdfFrame = $block.find('.pdfFrame');
    const $pdfPlaceholder = $block.find('.pdfPlaceholder');

    let currentPdfUrl = '';
    let currentMenuName = '';
    let isLoading = false;

    // Initialize with first available menu
    initializeFirstMenu();

    // Filter button functionality
    $filterButtons.on('click', function() {
        const category = $(this).data('category');
        filterMenus(category);

        // Update active filter button
        $filterButtons.removeClass('active');
        $(this).addClass('active');
    });

    // Menu item click functionality
    $menuItems.on('click', function(e) {
        e.preventDefault();
        if (!isLoading) {
            selectMenu($(this));
        }
    });

    // Download button functionality
    $downloadBtn.on('click', function() {
        if (currentPdfUrl && currentMenuName) {
            downloadPdf(currentPdfUrl, currentMenuName);
        }
    });

    function initializeFirstMenu() {
        // Find the first visible menu item and select it
        const $firstVisibleMenu = $block.find('.menuItem:visible').first();
        if ($firstVisibleMenu.length) {
            selectMenu($firstVisibleMenu);
        }
    }

    function filterMenus(category) {
        // Hide all category groups first
        $categoryMenus.hide();

        // Show relevant categories
        if (category === 'all') {
            $categoryMenus.show();
        } else {
            $block.find(`[data-category="${category}"]`).show();
        }

        // Select first visible menu after filtering
        setTimeout(() => {
            initializeFirstMenu();
        }, 100);
    }

    function selectMenu($menuItem) {
        if (isLoading) return;

        // Remove active class from all menu items
        $menuItems.removeClass('active');

        // Add active class to selected item
        $menuItem.addClass('active');

        // Get PDF URL and menu name
        currentPdfUrl = $menuItem.data('pdf-url');
        currentMenuName = $menuItem.data('menu-name');

        // Load PDF
        if (currentPdfUrl) {
            loadPdf(currentPdfUrl);

            // Enable download button
            $downloadBtn.prop('disabled', false);
            $downloadBtn.find('.downloadText').text(`Download ${currentMenuName}`);
        }
    }

    function loadPdf(pdfUrl) {
        if (isLoading) return;

        isLoading = true;

        // Hide placeholder and show iframe
        $pdfPlaceholder.hide();
        $pdfFrame.show();

        // Create a new iframe to avoid loading issues
        const newSrc = pdfUrl + '#toolbar=0&navpanes=0&scrollbar=0&view=FitH';

        // Only reload if URL is different
        if ($pdfFrame.attr('src') !== newSrc) {
            // Add loading state with smooth transition
            $pdfFrame.css({
                'opacity': '0.3',
                'transition': 'opacity 0.3s ease'
            });

            // Set new source
            $pdfFrame.attr('src', newSrc);

            // Handle load completion
            $pdfFrame.off('load.pdfMenus').on('load.pdfMenus', function() {
                $(this).css('opacity', '1');
                isLoading = false;
            });

            // Fallback timeout in case load event doesn't fire
            setTimeout(() => {
                $pdfFrame.css('opacity', '1');
                isLoading = false;
            }, 2000);
        } else {
            isLoading = false;
        }
    }

    function downloadPdf(pdfUrl, menuName) {
        // Create a temporary link element for download
        const $link = $('<a>', {
            href: pdfUrl,
            download: `${menuName}.pdf`,
            target: '_blank'
        });

        // Trigger download
        $('body').append($link);
        $link[0].click();
        $link.remove();

        // Optional: Track download event
        if (typeof gtag !== 'undefined') {
            gtag('event', 'download', {
                'event_category': 'Menu',
                'event_label': menuName,
                'value': 1
            });
        }
    }

    // Responsive handling
    function handleResize() {
        const isMobile = $(window).width() <= 768;

        if (isMobile) {
            $block.addClass('mobile-layout');
        } else {
            $block.removeClass('mobile-layout');
        }
    }

    // Initial resize check
    handleResize();

    // Listen for resize events
    $(window).on('resize.pdfMenus', handleResize);
}

// Initialize all PDF menu blocks on page load
$(document).ready(function() {
    // This function will be called from the PHP template for each block instance
    console.log('PDF Menus Block JavaScript loaded');
});

// Initialize on Swup page transitions (if using Swup)
if (typeof swup !== 'undefined') {
    swup.on('contentReplaced', function() {
        // Re-initialize blocks after page transition
        $('.pdfMenusBlock').each(function() {
            const blockId = $(this).attr('id');
            if (blockId) {
                initPdfMenusBlock(blockId);
            }
        });
    });
}

// Initialize on custom page init event (if used in theme)
$(document).on('initPage', function() {
    $('.pdfMenusBlock').each(function() {
        const blockId = $(this).attr('id');
        if (blockId) {
            initPdfMenusBlock(blockId);
        }
    });
});
