var autoSlide;
var hammerDisabled = false;
$(document).ready(function(){
  $(document).on("initPage", function(){
    if ($(".homeHeaderBlock").length > 0) {
      initializeHomeHeaderBlock();
    }
  });
});

function initializeHomeHeaderBlock() {
  // make stickycols sticky:
  var showLogo = false;
  gsap.set(".homeHeaderBlock .stickyCols", {
    scrollTrigger: {
      trigger: ".homeHeaderBlock .stickyWrapper",
      start: "top top",
      end: "bottom bottom",
      scrub: true,
      onUpdate: function(e) {
        gsap.set(".homeHeaderBlock .imageRow", {
          height: e.progress * 100 + "vh",
        });
        // width from 40% to 100%
        gsap.set(".homeHeaderBlock .imageWrapper", {
          width: (e.progress * 70) + 30 + "%",
        });
        // current scale to .3
        gsap.set(".homeHeaderBlock .logo .toLeft", {
          x: (e.progress * -100) + "%",
        });
        gsap.set(".homeHeaderBlock .logo .toRight", {
          x: (e.progress * 100) + "%",
        });
        if (e.progress > 0.99) {
          showLogo = true;
          if (showLogo) {
            $("header").addClass("showLogo");
          }
          showLogo = true;
        } else {
          if (!showLogo) {
            $("header").removeClass("showLogo");
          }
          showLogo = false;
        }
      },
      onEnter: function() {
        $(".homeHeaderBlock .stickyCols").css({
          position: 'fixed',
        });
      },
      onLeave: function() {
        $(".homeHeaderBlock .stickyCols").css({
          position: 'absolute',
          top: 'auto',
          bottom: 0,
        });
      },
      onEnterBack: function() {
        $(".homeHeaderBlock .stickyCols").css({
          position: 'fixed',
        });
      },
      onLeaveBack: function() {
        $(".homeHeaderBlock .stickyCols").css({
          position: 'absolute',
          // top: 'auto',
          top: 0,
        });
      },
    },
  });
}

