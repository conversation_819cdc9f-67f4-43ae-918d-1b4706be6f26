$(document).ready(function() {
    $(document).on("initPage", function() {
        if ($(".imagesTextBlock").length > 0) {
            initImagesTextBlock();
        }
    });
});

function initImagesTextBlock() { 
    var isMobile = $(window).outerWidth() <= 580;
    $(".imagesTextBlock").each(function(i, el){
        var stickyElement = $(el).find(".stickyCols");
        var sizeCalculation = stickyElement.find(".imageItems .item").length == 1 ? 3 : 5;
        let settings = isMobile ? 0.0001 : 0.1;
        $(el).css("height", ($(el).find(".stickyCols").outerHeight()) * (sizeCalculation - 1));
        setSliderHeights();
        setTimeout(function() {
            gsap.to(stickyElement, settings, {
                width: 100 + "%",
                scrollTrigger: {
                    start: "top bottom",
                    end: "top+=" + ($(el).outerHeight() - $('header').outerHeight()) + " top",
                    trigger: stickyElement,
                    scrub: true,
                    onUpdate(self) {
                        $(el).find(".divider").css("transform", "scaleX(" + self.progress + ")");
                        var items = stickyElement.find(".imageItems .item");
                        if (items.length == 1) return;
                        var activeItem = Math.floor((items.length ) * self.progress);
                        $(".imagesTextBlock .item").removeClass("active");
                        $(".imagesTextBlock .imageItems .item").eq(activeItem).addClass("active");
                        $(".imagesTextBlock .titleItems .item").eq(activeItem).addClass("active");
                        $(".imagesTextBlock .textItems .item").eq(activeItem).addClass("active");
                    }
                }
            });
        }, 100);
        
    });
    if (!isMobile) {
        var $textWrapper = $('.imagesTextBlock .stickyCols');
        var $imageWrapper = $('.imagesTextBlock .stickyWrapper');
        var wrapperHeight = $imageWrapper.outerHeight();
        var originalOffsetTop = $textWrapper.offset().top;
        var textWrapperHeight = $textWrapper.outerHeight();

        scroller.on('scroll', function() {
            textWrapperHeight = $textWrapper.outerHeight();
            var scrollTop = $(window).scrollTop() + ($("header").outerHeight() * 1.5);
            var stickyStart = originalOffsetTop;
            var stickyEnd = stickyStart + wrapperHeight - textWrapperHeight;

            if (scrollTop >= stickyStart && scrollTop <= stickyEnd) {
            $textWrapper.css({
                top: (scrollTop - stickyStart) + 'px'
            });
            } else if (scrollTop > stickyEnd) {
            $textWrapper.css({
                top: (wrapperHeight - textWrapperHeight) + 'px'
            });
            } else {
            $textWrapper.css({
                top: '0px'
            });
            }
        });
    }
}

function setSliderHeights() {
    var heighest = 0;
    $(".imagesTextBlock .titleItems .item").each(function(i, el){
        if ($(el).outerHeight() > heighest) {
            heighest = $(el).outerHeight();
        }
    });
    $(".imagesTextBlock .titleItems").css("height", heighest);

    var heighest = 0;
    $(".imagesTextBlock .textItems .item").each(function(i, el){
        if ($(el).outerHeight() > heighest) {
            heighest = $(el).outerHeight();
        }
    });
    $(".imagesTextBlock .textItems").css("height", heighest);
}