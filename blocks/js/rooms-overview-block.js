$(document).ready(function(){
    $(document).on("initPage", function(){
        if ($(".roomsOverviewBlock").length > 0) {
            setTimeout(function() {
                setupRoomsOverviewBlock();
            }, 300);
        }
    });
});

function setupRoomsOverviewBlock() {
    var r = document.querySelector(':root');
    $("[data-room-color]").each(function(i, el) {
        // set background color from body to current data-room-color, based on scroll..
        var $roomWrapper = $(this);
        var roomColor = $(el).data("room-color").replace(";", "");
        scroller.on("scroll", function() {
            if (scrollY > ($roomWrapper.offset().top - $(window).outerHeight()) && scrollY < ($roomWrapper.offset().top - $(window).outerHeight()) + $roomWrapper.outerHeight()) {
                $('.roomsOverviewBlock').css("background-color", roomColor);
            }
        })
    });
}
