$(document).ready(function(){
  $(document).on("initPage", function(){
    initializeMenuHighlightBlock();
  });
});

function initializeMenuHighlightBlock() {
  if ($(".menuHightlightBlock").length > 0) {
    setTimeout(function() {
      setMenuHighlightBlock();
    }, 300);
  }
}

function setMenuHighlightBlock() {
  gsap.to(".menuHightlightBlock .backgroundImage img", {
    scale: 1.2,
    scrollTrigger: {
      trigger: ".menuHightlightBlock .backgroundImage",
      start: "top bottom",
      end: "bottom top",
      scrub: true,
      ease: "power2.out"
    },
  });
}
