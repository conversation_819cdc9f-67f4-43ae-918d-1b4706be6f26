var currentActiveIndex = 0;
var totalItems = 0;
var marqueeDirection = 'right';
var isInitialized = false;

$(document).ready(function(){
  $(document).on("initPage", function(){
    initializeRoomsMarqueeBlock();
  });
});

function initializeRoomsMarqueeBlock() {
  if ($(".roomsMarqueeBlock").length > 0) {
    setTimeout(function() {
      setRoomsMarqueeBlock();
    }, 300);
  }
}

function setRoomsMarqueeBlock() {
  $('.roomsMarqueeBlock').each(function() {
    var $block = $(this);
    var $marquee = $block.find('.marquee');
    var $items = $block.find('.item');
    var $backgrounds = $block.find('.background');
    var $current = $block.find('.sliderIndicator .current');
    var $total = $block.find('.sliderIndicator .total');
    var $innerBar = $block.find('.sliderIndicator .innerBar');
    var $prevArrow = $block.find('.sliderIndicator .arrow.prev');
    var $nextArrow = $block.find('.sliderIndicator .arrow.next');

    // Initialize variables
    totalItems = $items.length;
    currentActiveIndex = 0;
    marqueeDirection = $marquee.attr('data-marquee-direction') || 'right';

    // Set initial states
    $total.text(totalItems);
    $current.text(1);

    // Set initial background opacity
    $backgrounds.css('opacity', 0);
    $backgrounds.eq(0).css('opacity', 1);

    // Set initial indicator bar
    gsap.set($innerBar, { scaleX: 0 });

    // Add data-index to backgrounds if missing
    $backgrounds.each(function(index) {
      $(this).attr('data-index', index);
    });

    // Initialize arrow navigation
    initializeArrowNavigation($block, $marquee, $items, $backgrounds, $current, $innerBar, $prevArrow, $nextArrow);

    // Start monitoring marquee position
    if (!isInitialized) {
      startMarqueeMonitoring($block);
      isInitialized = true;
    }
  });
}

function initializeArrowNavigation($block, $marquee, $items, $backgrounds, $current, $innerBar, $prevArrow, $nextArrow) {
  // Event listeners voor arrow navigatie
  $prevArrow.off('click').on('click', function(e) {
    e.preventDefault();
    navigateToItem($block, $marquee, $items, $backgrounds, $current, $innerBar, 'prev');
  });

  $nextArrow.off('click').on('click', function(e) {
    e.preventDefault();
    navigateToItem($block, $marquee, $items, $backgrounds, $current, $innerBar, 'next');
  });

  // Add hover effects
  $prevArrow.add($nextArrow).css({
    'cursor': 'pointer',
    'transition': 'opacity 0.3s ease'
  });

  $prevArrow.add($nextArrow).hover(
    function() {
      $(this).css('opacity', '0.7');
    },
    function() {
      $(this).css('opacity', '1');
    }
  );
}

function navigateToItem($block, $marquee, $items, $backgrounds, $current, $innerBar, direction) {
  var newIndex;

  if (direction === 'next') {
    newIndex = (currentActiveIndex + 1) % totalItems;
  } else {
    newIndex = currentActiveIndex === 0 ? totalItems - 1 : currentActiveIndex - 1;
  }

  // Immediately update the active state for instant feedback
  updateActiveState($backgrounds, $current, $innerBar, newIndex);
  currentActiveIndex = newIndex;

  // Get all marquee containers (including clones)
  var $containers = $marquee.find('.itemsContainer');
  var $firstContainer = $containers.first();

  // Calculate target position for the item in the first container
  var $targetItem = $firstContainer.find('.item').eq(newIndex);

  if ($targetItem.length === 0) {
    console.warn('Target item not found for index:', newIndex);
    return;
  }

  var itemRect = $targetItem[0].getBoundingClientRect();
  var windowCenter = $(window).width() / 2;

  // Calculate how much we need to move to center the item
  var itemCenterX = itemRect.left + (itemRect.width / 2);
  var targetOffset = windowCenter - itemCenterX;

  // Apply the movement to all containers to maintain seamless loop
  gsap.to($containers, {
    x: '+=' + targetOffset,
    duration: 0.8,
    ease: "power2.out"
  });
}

function startMarqueeMonitoring($block) {
  var $marquee = $block.find('.marquee');
  var $items = $block.find('.item');
  var $backgrounds = $block.find('.background');
  var $current = $block.find('.sliderIndicator .current');
  var $innerBar = $block.find('.sliderIndicator .innerBar');

  // Create a GSAP ticker to monitor positions
  var tickerFunction = function() {
    checkActiveItem($block, $marquee, $items, $backgrounds, $current, $innerBar);
  };

  gsap.ticker.add(tickerFunction);

  // Store reference for cleanup if needed
  $block.data('ticker-function', tickerFunction);
}

function checkActiveItem($block, $marquee, $items, $backgrounds, $current, $innerBar) {
  var windowCenter = $(window).width() / 2;
  var newActiveIndex = currentActiveIndex;
  var closestDistance = Infinity;

  // Check all items across all containers to find the one closest to center
  $marquee.find('.item').each(function() {
    var $item = $(this);
    var itemOffset = $item.offset();

    if (!itemOffset) return; // Skip if item is not visible

    var itemLeft = itemOffset.left;
    var itemRight = itemLeft + $item.outerWidth();
    var itemCenter = itemLeft + ($item.outerWidth() / 2);

    // Get the original data-index from the item
    var itemIndex = parseInt($item.attr('data-index'));
    if (isNaN(itemIndex)) return;

    // Check if item crosses the center point
    var crossesCenter = itemLeft <= windowCenter && itemRight >= windowCenter;

    if (crossesCenter) {
      var distanceToCenter = Math.abs(itemCenter - windowCenter);

      // Find the item that's closest to center when crossing
      if (distanceToCenter < closestDistance) {
        closestDistance = distanceToCenter;
        newActiveIndex = itemIndex;
      }
    }
  });

  // Update active state if changed
  if (newActiveIndex !== currentActiveIndex && newActiveIndex >= 0 && newActiveIndex < totalItems) {
    updateActiveState($backgrounds, $current, $innerBar, newActiveIndex);
    currentActiveIndex = newActiveIndex;
  }
}

function updateActiveState($backgrounds, $current, $innerBar, activeIndex) {
  // Update background opacity - set all to 0 first
  $backgrounds.each(function() {
    gsap.to($(this), {
      opacity: 0,
      duration: 0.6,
      ease: "power2.out"
    });
  });

  // Set active background to opacity 1
  var $activeBackground = $backgrounds.filter('[data-index="' + activeIndex + '"]');
  if ($activeBackground.length === 0) {
    $activeBackground = $backgrounds.eq(activeIndex);
  }

  gsap.to($activeBackground, {
    opacity: 1,
    duration: 0.6,
    ease: "power2.out"
  });

  // Update current indicator
  $current.text(activeIndex + 1);

  // Update progress bar - scale from 0 to 1 based on progress
  var progress = totalItems > 1 ? activeIndex / (totalItems - 1) : 0;
  gsap.to($innerBar, {
    scaleX: progress,
    duration: 0.6,
    ease: "power2.out"
  });
}

// Cleanup function if needed
function cleanupRoomsMarqueeBlock($block) {
  var tickerFunction = $block.data('ticker-function');
  if (tickerFunction) {
    gsap.ticker.remove(tickerFunction);
    $block.removeData('ticker-function');
  }
}
