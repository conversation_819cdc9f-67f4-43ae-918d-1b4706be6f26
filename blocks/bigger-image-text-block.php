<?php
$size = 'large';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = str_replace(['<p>', '</p>'], '', get_field("title"));

// Voer regex vervanging uit op de title (indien nodig)
$title = preg_replace($pattern, $replacement, $title);
?>

<section class="biggerImageTextBlock" data-init>
  <div class="contentWrapper">
    <div class="cols">
      <div class="col text">
        <div class="subTitle primary">
          <?php echo esc_html(get_field("subtitle")); ?>
        </div>
        <h2 class="title splitThis" data-lines data-words><?php echo $title; ?></h2>
        <div class="text">
          <p>
          <?php echo get_field("text"); ?>
          </p>
        </div>
      </div>
        <div class="col image">
          <div class="imageWrapper">
            <div class="innerImage">
              <div class="innerTransform" data-parallax data-parallax-speed="1">
                <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
              </div>  
            </div>
          </div>
        </div>
    </div>
  </div>
</section>