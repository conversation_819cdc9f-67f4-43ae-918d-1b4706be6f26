<?php
$size = 'large';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = str_replace(['<p>', '</p>'], '', get_field("title"));
$title = preg_replace($pattern, $replacement, $title);
?>

<section class="articlesBlock" data-init>
  <div class="contentWrapper">
    <div class="card">
      <div class="cols sliderWrapper">
          <div class="col text">
            <h2 class="title splitThis" data-lines data-words><?php echo $title; ?></h2>
            <div class="text">
              <p>
              <?php echo get_field("text"); ?>
              </p>
            </div>
            <?php
              if( get_field("link") ):
              $link = get_field("link");
              if( $link ) {
                  $link_url = $link['url'];
                  $link_title = $link['title'];
                  $link_target = $link['target'] ? $link['target'] : '_self';
              } ?>
            <?php include("parts/textlink.php"); ?>
            <?php endif; ?>
            <div class="arrows">
              <div class="sliderButton prev" data-prev><i class="icon-arrow-left"></i></div>
              <div class="sliderButton next" data-next><i class="icon-arrow-right"></i></div>
            </div>
          </div>
          <div class="col">
            <div class="slider" data-slider>
              <?php if( have_rows('articles') ): ?>
                <?php while( have_rows('articles') ): the_row(); ?>
                  <?php $image = get_sub_field('image'); ?>
                  <?php $title = get_sub_field('title'); ?>
                  <?php $text = get_sub_field('text'); ?>
                  <?php $link = get_sub_field('link'); ?>
                  <div class="slide">
                    <a class="innerLink" href="<?php echo esc_url($link['url']); ?>" target="_blank">
                      <div class="imageWrapper">
                        <div class="innerImage">
                          <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
                        </div>
                      </div>
                      <div class="innerContent">
                        <h3 class="subTitle white"><?php echo $title; ?></h3>
                      </div>
                    </a>
                  </div>
                <?php endwhile; ?>
              <?php endif; ?>
            </div>
          </div>
      </div>
    </div>
  </div>
</section>