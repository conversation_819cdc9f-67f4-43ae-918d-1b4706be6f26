<?php
$size = 'large';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = str_replace(['<p>', '</p>'], '', get_field("title"));

// Voer regex vervanging uit op de title (indien nodig)
$title = preg_replace($pattern, $replacement, $title);
?>

<section class="imageTextBlock<?php if (get_field('image_right')) { ?> imageRight<?php } ?>" data-init>
  <div class="contentWrapper">
    <div class="cols">
      <?php if (!get_field("image_right") && $image) { ?>
        <div class="col image">
          <div class="imageWrapper">
            <div class="innerImage">
              <div class="innerTransform" data-parallax data-parallax-speed="1">
                <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
              </div>  
            </div>
          </div>
        </div>
      <?php } ?>
      <div class="col text">
        <div class="subTitle primary">
          <?php echo esc_html(get_field("subtitle")); ?>
        </div>
        <h2 class="title splitThis" data-lines data-words><?php echo $title; ?></h2>
        <div class="text">
          <p>
          <?php echo get_field("text"); ?>
          </p>
        </div>
         <?php
          $link = get_field("link");
          if( $link ) {
              $link_url = $link['url'];
              $link_title = $link['title'];
              $link_target = $link['target'] ? $link['target'] : '_self';
          ?>
          <div class="buttonWrapper">
            <a class="button" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
            
            <? $link2 = get_field("link_2");
                if( $link2 ) {
                    $link_url = $link2['url'];
                    $link_title = $link2['title'];
                    $link_target = $link2['target'] ? $link2['target'] : '_self';
                ?>
                <a class="button" href="<?php echo esc_url( $link_url ); ?>" target="<?php echo esc_attr( $link_target ); ?>"><?php echo esc_html( $link_title ); ?></a>
            <? } ?>
            <?php if (get_field("extra_text")) { ?>
              <div class="extraText text">
                <p><?php echo get_field("extra_text"); ?></p>
              </div>
            <?php } ?>
          </div>
          <?php } ?>
      </div>
      <?php if (get_field("image_right") && $image) { ?>
        <div class="col image">
          <div class="imageWrapper">
            <div class="innerImage">
              <div class="innerTransform" data-parallax data-parallax-speed="1">
                <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
              </div>
            </div>
          </div>
        </div>
      <?php } ?>
    </div>
  </div>
</section>