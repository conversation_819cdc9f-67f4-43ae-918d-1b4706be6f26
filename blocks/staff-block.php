<?php
$size = 'medium_large'; // (thumbnail, medium, large, full or custom size)
$members = get_posts(array(
    'posts_per_page' => 99,
    'order' => 'ASC',
    'post_type'     => 'member',
));
$the_query = new WP_Query( $members );
$title = str_replace(['<p>', '</p>'], '', get_field("title"));
?>
<section class="staffBlock black<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper smaller">
    <div class="innerContent">
      <h2 class="bigTitle splitThis" data-init data-split><?php echo $title; ?></h2>
      <div class="text grey">
        <?php the_field("text") ?>
      </div>
    </div>
    <div class="members" data-slider>
      <?php if( $members ): ?>
          <?php 
          $highlighted_members = [];
          $regular_members = [];
          foreach( $members as $post ): 
              setup_postdata($post);
              $highlighted = get_field('highlighted', $post->ID);
              if ($highlighted) {
                  $highlighted_members[] = $post;
              } else {
                  $regular_members[] = $post;
              }
          endforeach; 
          $members = array_merge($highlighted_members, $regular_members);
          foreach( $members as $post ): 
              setup_postdata($post);
              $title = get_the_title($post->ID);
              $image = get_field( 'image', $post->ID );
              $function = get_field( 'function', $post->ID );
              $description = get_field( 'description', $post->ID );
              $highlighted = get_field('highlighted', $post->ID);
              ?>
              <div class="member<?php if ($highlighted): ?> highlighted<? endif; ?>">
                <div class="innerCol">
                  <div class="imageWrapper">
                    <div class="innerImage">
                      <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
                    </div>
                  </div>
                </div>
                <div class="innerCol">
                  <h3 class="subTitle"><span class="primary"><?php echo esc_html( $title ); ?></span> | <?php echo esc_html( $function ); ?></h3>
                  <div class="text white smaller"><p><?php echo esc_html( $description ); ?></p></div>
                </div>
              </div>
          <?php endforeach; ?>
          <?php wp_reset_postdata(); ?>
      <?php endif; ?>
    </div>
  </div>
</section>