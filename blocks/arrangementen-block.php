<?php
$size = 'large'; // (thumbnail, medium, large, full or custom size)
$arrangementen = get_posts(array(
    'posts_per_page' => -1,
    'order' => 'ASC',
    'post_type'     => 'arrangement',
));
$the_query = new WP_Query( $arrangementen );
$title = str_replace(['<p>', '</p>'], '', get_field("title"));
?>
<section class="arrangementenBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper smaller">
    <div class="innerContent">
      <?php if (get_field("subtitle")) {?><div class="subTitle primary"><?php the_field("subtitle") ?></div><?php } ?>
      <?php if (get_field("title")) {?><h2 class="bigTitle splitThis" data-init data-split><?php echo $title; ?></h2><?php } ?>
      <?php if (get_field("text")) {?>
        <div class="text">
          <?php the_field("text") ?>
        </div>
      <?php } ?>
    </div>
    <div class="items">
      <?php $taxonomy = get_field("tags");
          if( $arrangementen ): ?>
          <?php foreach( $arrangementen as $post ): setup_postdata($post);
            $title = get_the_title($post->ID);
            $image = get_field( 'image', $post->ID );
            $list_items = get_field('list_item', $post->ID);
            if (!has_term($taxonomy, 'post_tag', $post->ID)) {
                continue;
            }
          ?>
            <div class="item">
              <div class="imageWrapper">
                <div class="innerImage">
                  <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
                </div>
              </div>
              <h3 class="subTitle primary"><?php echo esc_html( $title ); ?></h3>
              <?php if( $list_items ): // Controleer of er items zijn ?>
                <ul class="listItems">
                    <?php foreach( $list_items as $list_item ): ?>
                        <li>
                            <p><i class="icon-bolwerk"></i><span class="innerText"><?php echo esc_html($list_item['text']); ?></span></p>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
            </div>
          <?php endforeach; ?>
          <?php wp_reset_postdata(); ?>
      <?php endif; ?>
    </div>
  </div>
</section>
