<?php
/**
 * Rooms Overview Block Template
 *
 * Displays all room page content under each other
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'rooms-overview-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'rooms-overview-block';
if (!empty($block['className'])) {
    $className .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $className .= ' align' . $block['align'];
}

// Query all published rooms
$rooms_query = new WP_Query(array(
    'post_type' => 'rooms',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'orderby' => 'menu_order',
    'order' => 'ASC'
));
?>
<!-- first room background color -->
<div id="<?php echo esc_attr($id); ?>" class="roomsOverviewBlock" style="background-color: <?php echo esc_attr(get_field('room_color', $rooms_query->posts[0]->ID)); ?>;">
    <?php if ($rooms_query->have_posts()) : ?>
        <div class="roomsOverviewContent">
            <?php while ($rooms_query->have_posts()) : $rooms_query->the_post(); ?>
                <?php
                // Get room color and create anchor
                $room_color = get_field('room_color', get_the_ID());
                $room_slug = get_post_field('post_name', get_the_ID());
                $room_title = get_the_title();
                ?>
                <div class="roomWrapper" data-init data-room-color="<?php echo esc_attr($room_color); ?>" data-anchor="<?php echo esc_attr($room_slug); ?>" id="room-<?php echo esc_attr($room_slug); ?>">
                    <div class="room-anchor-target" data-anchor="<?php echo esc_attr($room_slug); ?>"></div>
                    <?php the_content(); ?>
                </div>
            <?php endwhile; ?>
            </div>
        <?php wp_reset_postdata(); ?>
    <?php endif; ?>
</div>
