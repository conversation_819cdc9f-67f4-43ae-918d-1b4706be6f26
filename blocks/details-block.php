<?php
/**
 * Details Block Template
 *
 * @param array $block The block settings and attributes.
 * @param string $content The block inner HTML (empty).
 * @param bool $is_preview True during AJAX preview.
 * @param int|string $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'details-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'detailsBlock';
if (!empty($block['className'])) {
    $className .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $className .= ' align' . $block['align'];
}

// Load values and assign defaults.
$col1_title = get_field('col1_title');
$col1_content = get_field('col1_content');
$col2_title = get_field('col2_title');
$col2_content = get_field('col2_content');
$col3_title = get_field('col3_title');
$col3_content = get_field('col3_content');

/**
 * Helper function to process text content and add subTitle class to headings
 */
function process_text_content($content) {
    if (empty($content)) {
        return '';
    }
    
    // Add subTitle class to h1-h6 headings
    $content = preg_replace('/<(h[1-6])([^>]*)>/', '<$1$2 class="subTitle">', $content);
    
    return $content;
}

?>

<section id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>" data-init>
    <div class="contentWrapper">
        <div class="cols">
            <!-- Column 1 -->
            <div class="col">
                <?php if (!empty($col1_title)): ?>
                    <h3 class="title" data-lines data-words><?php echo esc_html($col1_title); ?></h3>
                <?php else: ?>
                    <h3 class="title">&nbsp;</h3>
                <?php endif; ?>
                
                <?php if ($col1_content): ?>
                    <div class="content">
                        <?php foreach ($col1_content as $text_block): ?>
                            <?php if (!empty($text_block['text_block'])): ?>
                                <div class="text">
                                    <?php echo process_text_content($text_block['text_block']); ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Column 2 -->
            <div class="col">
                <?php if (!empty($col2_title)): ?>
                    <h3 class="title" data-lines data-words><?php echo esc_html($col2_title); ?></h3>
                <?php else: ?>
                    <h3 class="title">&nbsp;</h3>
                <?php endif; ?>
                
                <?php if ($col2_content): ?>
                    <div class="content">
                        <?php foreach ($col2_content as $text_block): ?>
                            <?php if (!empty($text_block['text_block'])): ?>
                                <div class="text">
                                    <?php echo process_text_content($text_block['text_block']); ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Column 3 -->
            <div class="col">
                <?php if (!empty($col3_title)): ?>
                    <h3 class="title" data-lines data-words><?php echo esc_html($col3_title); ?></h3>
                <?php else: ?>
                    <h3 class="title">&nbsp;</h3>
                <?php endif; ?>
                
                <?php if ($col3_content): ?>
                    <div class="content">
                        <?php foreach ($col3_content as $text_block): ?>
                            <?php if (!empty($text_block['text_block'])): ?>
                                <div class="text">
                                    <?php echo process_text_content($text_block['text_block']); ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
