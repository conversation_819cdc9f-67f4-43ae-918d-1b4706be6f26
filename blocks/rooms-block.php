<?php
/**
 * Rooms Block Template
 *
 * @param array $block The block settings and attributes.
 * @param string $content The block inner HTML (empty).
 * @param bool $is_preview True during AJAX preview.
 * @param int|string $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'rooms-block-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'rooms-block';
if (!empty($block['className'])) {
    $className .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $className .= ' align' . $block['align'];
}

// Get block fields
$display_type = get_field('display_type') ?: 'grid';
$rooms_to_show = get_field('rooms_to_show') ?: 'all';
$selected_rooms = get_field('selected_rooms');
$show_price = get_field('show_price') ?: true;
$show_capacity = get_field('show_capacity') ?: true;
$show_amenities = get_field('show_amenities') ?: false;
$columns = get_field('columns') ?: 3;

// Query rooms
$args = array(
    'post_type' => 'rooms',
    'post_status' => 'publish',
    'posts_per_page' => -1,
    'orderby' => 'menu_order',
    'order' => 'ASC'
);

if ($rooms_to_show === 'selected' && !empty($selected_rooms)) {
    $args['post__in'] = $selected_rooms;
}

$rooms_query = new WP_Query($args);
?>

<div id="<?php echo esc_attr($id); ?>" class="<?php echo esc_attr($className); ?>">
    <?php if ($rooms_query->have_posts()) : ?>
        <div class="rooms-container rooms-<?php echo esc_attr($display_type); ?> rooms-columns-<?php echo esc_attr($columns); ?>">
            <?php while ($rooms_query->have_posts()) : $rooms_query->the_post(); 
                // Get room fields
                $room_color = get_field('room_color');
                $room_title = get_field('room_title') ?: get_the_title();
                $room_description = get_field('room_description');
                $room_gallery = get_field('room_gallery');
                $room_amenities = get_field('room_amenities');
                $room_price = get_field('room_price');
                $room_capacity = get_field('room_capacity');
                $room_size = get_field('room_size');
                
                // Get featured image as fallback
                $featured_image = get_the_post_thumbnail_url(get_the_ID(), 'large');
                $main_image = !empty($room_gallery) ? $room_gallery[0]['sizes']['large'] : $featured_image;
            ?>
                <div class="room-item" style="<?php echo $room_color ? '--room-color: ' . esc_attr($room_color) . ';' : ''; ?>">
                    <?php if ($main_image) : ?>
                        <div class="room-image">
                            <img src="<?php echo esc_url($main_image); ?>" alt="<?php echo esc_attr($room_title); ?>" loading="lazy">
                            <?php if (!empty($room_gallery) && count($room_gallery) > 1) : ?>
                                <div class="room-gallery-indicator">
                                    <span class="gallery-count">+<?php echo count($room_gallery) - 1; ?> more</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="room-content">
                        <div class="room-header">
                            <h3 class="room-title"><?php echo esc_html($room_title); ?></h3>
                            <?php if ($show_price && $room_price) : ?>
                                <div class="room-price">€<?php echo number_format($room_price, 2); ?><span class="price-period">/night</span></div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($room_description) : ?>
                            <div class="room-description">
                                <?php echo wp_kses_post($room_description); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="room-details">
                            <?php if ($show_capacity && $room_capacity) : ?>
                                <div class="room-detail">
                                    <span class="detail-icon">👥</span>
                                    <span class="detail-text"><?php echo esc_html($room_capacity); ?> guests</span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($room_size) : ?>
                                <div class="room-detail">
                                    <span class="detail-icon">📐</span>
                                    <span class="detail-text"><?php echo esc_html($room_size); ?>m²</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($show_amenities && !empty($room_amenities)) : ?>
                            <div class="room-amenities">
                                <h4 class="amenities-title">Amenities</h4>
                                <div class="amenities-list">
                                    <?php foreach ($room_amenities as $amenity) : ?>
                                        <div class="amenity-item">
                                            <?php if (!empty($amenity['amenity_icon'])) : ?>
                                                <img src="<?php echo esc_url($amenity['amenity_icon']['sizes']['thumbnail']); ?>" alt="<?php echo esc_attr($amenity['amenity_name']); ?>" class="amenity-icon">
                                            <?php endif; ?>
                                            <span class="amenity-name"><?php echo esc_html($amenity['amenity_name']); ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="room-actions">
                            <?php
                            $room_slug = get_post_field('post_name', get_the_ID());
                            $rooms_page_url = '/rooms/'; // Adjust this to your actual rooms page URL
                            ?>
                            <a href="<?php echo esc_url($rooms_page_url . '#' . $room_slug); ?>" class="room-link btn btn-primary hashtagLink">View Details</a>
                        </div>
                    </div>
                    
                    <?php if (!empty($room_gallery) && count($room_gallery) > 1) : ?>
                        <div class="room-gallery-modal" style="display: none;">
                            <div class="gallery-overlay">
                                <div class="gallery-container">
                                    <button class="gallery-close">&times;</button>
                                    <div class="gallery-slider">
                                        <?php foreach ($room_gallery as $image) : ?>
                                            <div class="gallery-slide">
                                                <img src="<?php echo esc_url($image['sizes']['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>">
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="gallery-nav">
                                        <button class="gallery-prev">‹</button>
                                        <button class="gallery-next">›</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endwhile; ?>
        </div>
        
        <?php wp_reset_postdata(); ?>
    <?php else : ?>
        <div class="no-rooms-message">
            <p>No rooms found.</p>
        </div>
    <?php endif; ?>
</div>

<style>
.rooms-block {
    margin: 2rem 0;
}

.rooms-container {
    display: grid;
    gap: 2rem;
}

.rooms-grid.rooms-columns-1 { grid-template-columns: 1fr; }
.rooms-grid.rooms-columns-2 { grid-template-columns: repeat(2, 1fr); }
.rooms-grid.rooms-columns-3 { grid-template-columns: repeat(3, 1fr); }
.rooms-grid.rooms-columns-4 { grid-template-columns: repeat(4, 1fr); }

.room-item {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.room-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.room-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.room-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.room-item:hover .room-image img {
    transform: scale(1.05);
}

.room-gallery-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
}

.room-content {
    padding: 1.5rem;
}

.room-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.room-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--room-color, #333);
}

.room-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--room-color, #2c3e50);
}

.price-period {
    font-size: 0.9rem;
    font-weight: 400;
    color: #666;
}

.room-description {
    margin-bottom: 1rem;
    color: #666;
    line-height: 1.6;
}

.room-details {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.room-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.detail-icon {
    font-size: 1rem;
}

.room-amenities {
    margin-bottom: 1.5rem;
}

.amenities-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.amenities-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.amenity-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    background: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    color: #666;
}

.amenity-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

.room-actions {
    text-align: center;
}

.room-link {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: var(--room-color, #3498db);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.room-link:hover {
    background: var(--room-color, #2980b9);
    filter: brightness(0.9);
}

.no-rooms-message {
    text-align: center;
    padding: 3rem;
    color: #666;
}

@media (max-width: 768px) {
    .rooms-grid {
        grid-template-columns: 1fr !important;
    }
    
    .room-header {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .room-details {
        flex-direction: column;
        gap: 0.5rem;
    }
}
</style>
