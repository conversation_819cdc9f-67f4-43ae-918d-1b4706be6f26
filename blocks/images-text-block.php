<?php
$size = 'large';
$items = get_field('items');
?>

<section class="imagesTextBlock" data-init>
  <div class="contentWrapper small">
    <div class="stickyWrapper">
    <div class="stickyCols">
      <div class="cols">
          <div class="col image">
            <div class="imageItems">
               <div class="imageWrapper">
                  <div class="innerImage">
                  <?php $counter = 0; ?>
                  <?php foreach ($items as $item): ?>
                    <div class="item<?php if ($counter == 0) { ?> active<?php } ?>">
                      <?php $image = $item['image']; ?>
                      <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
                    </div>
                     <?php $counter++; ?>
                  <?php endforeach; ?>
                  </div>
              </div>
            </div>
          </div>
          <div class="col text">
            <div class="titleItems">
              <?php $counter = 0; ?>
              <?php foreach ($items as $item): ?>
                <div class="item<?php if ($counter == 0) { ?> active<?php } ?>">
                  <?php $title = $item['title']; ?>
                  <h2 class="title splitThis" data-lines data-words><?php echo $title; ?></h2>
                </div>
                <?php $counter++; ?>
              <?php endforeach; ?>
            </div>
            <div class="divider"></div>
            <div class="textItems">
              <?php $counter = 0; ?>
              <?php foreach ($items as $item): ?>
                <div class="item<?php if ($counter == 0) { ?> active<?php } ?>">
                  <?php $text = $item['text']; ?>
                  <div class="text">
                    <p>
                    <?php echo $text; ?>
                    </p>
                  </div>
                </div>
                <?php $counter++; ?>
              <?php endforeach; ?>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>