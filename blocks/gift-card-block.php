<?php
$size = 'large';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = str_replace(['<p>', '</p>'], '', get_field("title"));

// Voer regex vervanging uit op de title (indien nodig)
$title = preg_replace($pattern, $replacement, $title);
?>

<section class="giftCardBlock" data-init>
  <div class="contentWrapper small">
    <div class="card">
      <div class="cols">
          <div class="col image">
            <div class="imageWrapper">
              <div class="innerImage">
                <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
              </div>
            </div>
          </div>
          <div class="col text white">
            <h2 class="title splitThis white" data-lines data-words><?php echo $title; ?></h2>
            <div class="text white">
              <p>
              <?php echo get_field("text"); ?>
              </p>
            </div>
            <?php
              if( get_field("link") ):
              $link = get_field("link");
              if( $link ) {
                  $link_url = $link['url'];
                  $link_title = $link['title'];
                  $link_target = $link['target'] ? $link['target'] : '_self';
              } ?>
            <?php include("parts/textlink.php"); ?>
            <?php endif; ?>
          </div>
      </div>
    </div>
  </div>
</section>