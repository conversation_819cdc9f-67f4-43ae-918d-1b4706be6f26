<?php
  $size = 'medium'; // (thumbnail, medium, large, full or custom size)
  $gallery = get_field('image_gallery');
  $pattern = '/(.*)\*(.*)\*(.*)/';
  $replacement = '$1<span class="primary">$2</span>$3';
  $uid = uniqid('', true);

?>

<section class="galleryBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init data-init-delay=300>
  <div class="contentWrapper">
    <div class="bigLogoWrapper">
      <div class="svgWrapper">
        <?php include("parts/big_logo.php");  ?>
      </div>
    </div>
    <div class="gallery">
      <?php
        if ($gallery) {
          $delay = 0;
          $counter = 0;
          foreach ($gallery as $index => $image) {
              // Zorg ervoor dat je niet meer dan 7 items toont
              if ($index < 7) {
                ?>
                <div class="imageItem" <?php if ($counter == 0) { ?> data-parallax data-parallax-speed="0.2" <?php } ?><?php if ($counter == 6) { ?> data-parallax data-parallax-speed="-1" <?php } ?> data-index="<?php echo $counter; ?>" data-init data-init-delay="<?php echo esc_attr($delay); ?>" data-gallery-id="<?php echo($uid); ?>" data-gallery data-image="<?php echo esc_url($image['sizes']['medium_large']); ?>">
                    <div class="innerImage">
                        <div class="innerMask">
                          <img class="lazy" data-src="<?php echo esc_url($image['sizes']['medium_large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                        </div>
                    </div>
                </div>
                <?php
                $delay += 150;
                $counter += 1;
              }
          }
        } else {
            echo 'Geen afbeeldingen gevonden.';
        }
       ?>
    </div>
  </div>
</section>
<div class="overlay" style="display: none;" data-gallery-id="<?php echo($uid); ?>">
  <span class="background"></span>
  <span class="close">&times;</span>
  <div class="overlayContent">
    <div class="overlayImage">
        <img src="" alt="" id="overlayImg">
    </div>
  </div>
  <div class="navButton prev"><i class="icon-long-arrow-left"></i></div>
  <div class="navButton next"><i class="icon-long-arrow-right"></i></div>
</div>