<?php
$size = 'full';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = str_replace(['<p>', '</p>'], '', get_field("title"));
$args = array(
    'post_type' => 'tijd',
    'order' => 'ASC',
    'posts_per_page' => -1,
);
$tijden_query = new WP_Query($args);

$tijden = $tijden_query->posts;
?>

<section id="<?php the_field("id") ?>" class="openingTimesBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper smaller">
    <div class="subTitle primary"><?php the_field("subtitle") ?></div>
    <h2 class="bigTitle splitThis" data-init data-split><?php echo $title; ?></h2>
    <div class="cols">
      <div class="col">
        <div class="text">
          <?php the_field("text") ?>
        </div>
      </div>
      <div class="col">
        <?php
          if (!empty($tijden)) {
              foreach ($tijden as $post) {
                  setup_postdata($post);

                  if (have_rows('item', $post->ID)) {
                      echo '<div class="times">';
                      echo '<h2 class="subTitle">' . get_the_title($post->ID) . '</h2>';

                      echo '<div class="items">';
                      while (have_rows('item', $post->ID)) {
                          the_row();
                          $day = get_sub_field('day');
                          $time = get_sub_field('times');

                          echo '<span class="item subTitle"><span class="day">' . esc_html($day) . '</span><span class="time">' . esc_html($time) . '</span></span>';
                      }
                      echo '</div>';
                      echo '</div>';
                  }
              }
              wp_reset_postdata();
          } else {
              echo '<p>' . __('Geen openingstijden gevonden.', 'text-domain') . '</p>';
          }
        ?>
      </div>
    </div>
  </div>
</section>
