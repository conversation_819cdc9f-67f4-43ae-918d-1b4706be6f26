<?php
$size = 'large';
$images = get_field("images");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = str_replace(['<p>', '</p>'], '', get_field("title"));
$title = preg_replace($pattern, $replacement, $title);
?>

<section class="imagesSliderBlock" data-init>
  <div class="contentWrapper">
    <div class="sliderWrapper">
      <div class="slider" data-slider>
        <?php if( $images ): ?>
          <?php foreach( $images as $image ): ?>
            <div class="slide">
              <div class="imageWrapper">
                <div class="innerImage">
                  <img class="lazy" data-src="<?php echo esc_url($image['sizes'][$size]); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
                </div>
              </div>
            </div>
          <?php endforeach; ?>
        <?php endif; ?>
      </div>
      <div class="sliderIndicator"><div class="innerBar"></div></div>
    </div>
  </div>
</section>