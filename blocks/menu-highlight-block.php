<?php
$size = 'large'; // (thumbnail, medium, large, full or custom size)

  $menu = get_field('menu');
  $image = get_field('menu_image', $menu->ID);
  $background = get_field('image', $menu->ID);
  $pdf_link = get_field('menu_pdf', $menu->ID);
  $uid = uniqid('', true);
?>
<section class="menuHightlightBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper">
    <div class="imagesWrapper">
      <div class="backgroundImage">
        <img class="lazy" data-src="<?php echo esc_url($background['url']); ?>" alt="<?php echo esc_attr($background['alt']); ?>" />
      </div>
      <?php if ($image): ?>
        <img class="menu lazy" data-src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
      <?php endif; ?>
    </div>
  </div>
</section>
