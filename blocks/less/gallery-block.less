.galleryBlock {
  &.inview {
    .bigLogoWrapper {
      opacity: .05;
    }
  }
  .bigLogoWrapper {
    position: absolute;
    width: (@vw112 * 6) + (@vw16 * 5);
    max-width: 100%;
    top: 50%;
    left: 50%;
    right: 0;
    margin: auto;
    opacity: 0;
    z-index: -1;
    transform: translate(-50%,-50%) scale(1);
    pointer-events: none;
    will-change: transform;
    .transitionMore(all, .3s, 1.2s);
    .svgWrapper {
      animation: rotate360 60s linear infinite;
    }
    svg {
      width: 100%;
      height: 100%;
      object-fit: cover;
      path {
        fill: @hardBlack;
      }
    }
  }
  .gallery {
    position: relative;
    height: (@vw100 * 12) + @vw40;
    .imageItem {
      cursor: pointer;
      &:nth-child(1) {
        left: @vw112 + @vw16;
        top: @vw100 + @vw100 + @vw50;
        width: (@vw112 * 3) + (@vw16 * 2);
        .innerImage {
          padding-bottom: 100%;
        }
      }
      &:nth-child(2) {
        left: (@vw112 * 5) + (@vw16 * 5);
        top: 0;
        width: (@vw112 * 2) + @vw16;
        .innerImage {
          .padding-bottom-generator(240.09, 397.58);
        }
      }
      &:nth-child(3) {
        left: auto;
        right: 0;
        top: @vw100 * 2;
        width: (@vw112 * 2) + @vw16;
        .innerImage {
          padding-bottom: 100%;
        }
      }
      &:nth-child(4) {
        left: 0;
        top: auto;
        bottom: @vw100 + @vw40;
        width: @vw112 + @vw16;
        .innerImage {
          .padding-bottom-generator(128, 240);
        }
      }
      &:nth-child(5) {
        left: (@vw112 * 3) + (@vw16 * 3);
        top: auto;
        bottom: 0;
        width: (@vw112 * 2) + (@vw16 * 2);
        .innerImage {
          padding-bottom: 100%;
        }
      }
      &:nth-child(6) {
        left: auto;
        right: (@vw112 * 2) + (@vw16 * 2);
        top: auto;
        bottom: @vw100 + @vw90;
        width: (@vw112 * 4) + (@vw16 * 4);
        .innerImage {
          padding-bottom: 100%;
        }
      }
      &:nth-child(7) {
        left: auto;
        right: 0;
        bottom: @vw60;
        top: auto;
        width: @vw112 + (@vw16 * 3);
        .innerImage {
          .padding-bottom-generator(178, 300);
        }
      }
      * {
        cursor: pointer;
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .galleryBlock {
    .bigLogoWrapper {
      width: calc((@vw112-580 * 6) + (@vw16-580 * 5));
    }
    .gallery {
      height: calc((@vw100-580 * 14) + @vw40-580);
      .imageItem {
        &:nth-child(1) {
          left: calc(@vw112-580 + @vw16-580);
          top: calc((@vw100-580 * 2) + @vw50-580);
          width: calc((@vw112-580 * 2) + (@vw16-580 * 2));
        }
        &:nth-child(2) {
          left: 0;
          width: calc((@vw112-580 * 1) + @vw16-580);
        }
        &:nth-child(3) {
          top: @vw40-580;
          width: calc(@vw112-580 + @vw16-580);
        }
        &:nth-child(4) {
          bottom: 0;
          width: calc(@vw112-580 + @vw16-580);
        }
        &:nth-child(5) {
          left: calc((@vw112-580 * 3) + (@vw16-580 * 3));
          width: calc((@vw112-580 * 2) + (@vw16-580 * 2));
        }
        &:nth-child(6) {
          right: calc((@vw112-580 * 2) + (@vw16-580 * 2));
          bottom: calc(@vw112-580 + @vw112-580 + @vw112-580);
          width: calc((@vw112-580 * 4) + (@vw16-580 * 4));
        }
        &:nth-child(7) {
          right: 0;
          bottom: calc(@vw100-580 * 3);
          width: calc(@vw112-580 + (@vw16-580 * 3));
        }
      }
    }
  }
}
