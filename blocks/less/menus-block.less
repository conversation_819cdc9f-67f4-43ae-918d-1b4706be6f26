// out: false
.menusBlock {
  &.inview {
    .menus {
      .menu {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }
  &:has(.slider) {
    .menus {
      display: block;
    }
  }
  .sliderButton {
    position: absolute;
    top: 50%;
    &.next {
      right: 0;
      transform: translateY(-50%) translateX(50%) rotate(-90deg);
    }
    &.prev {
      left: 0;
      transform: translateY(-50%) translateX(-50%) rotate(90deg);
    }
  }
  .sliderIndicator {
    margin-top: @vw50;
    width: (@vw112 * 3) + (@vw16 * 3);
    overflow: hidden;
    height: 2px;
    position: relative;
    background: rgba(255,255,255,.2);
    .innerBar {
      background: @primaryColor;
      border-radius: @vw10;
      height: 100%;
      width: 0;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .menus {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin-left: -@vw16;
    width: calc(100% ~"+" @vw32);
    .bigLogoWrapper {
      position: absolute;
      width: (@vw112 * 6) + (@vw16 * 5);
      max-width: 100%;
      top: 0;
      opacity: .05;
      animation: rotate360 60s linear infinite;
      svg {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .menu {
      align-items: center;
      color: @hardBlack;
      display: inline-flex;
      flex-direction: column;
      flex-wrap: wrap;
      margin: 0 @vw16;
      text-align: center;
      text-decoration: none;
      vertical-align: middle;
      width: calc(33.3333% ~"-" @vw32);
      opacity: 0;
      transform: translateY(@vw20);
      .transition(.3s);
      &:hover {
        color: @primaryColor;
        .bigTitle {
          color: @primaryColor;
        }
        .arrow {
          &:before {
            width: 80%;
            background: @primaryColor;
          }
          &:after {
            border-color: @primaryColor;
          }
        }
        .imageWrapper {
          .innerImage {
            img {
              transform: translate(-50%,-50%) scale(1.1);
              .transitionMore(transform, 0.6s, 0s, cubic-bezier(0.34, 1.56, 0.64, 1));
            }
          }
        }
      }
      &:nth-child(even) {
        flex-direction: column-reverse;
        .bigTitle {
          margin: @vw30 0 @vw10 0;
        }
      }
      &:nth-child(odd) {
        transition-delay: .3s;
        .textLink {
          margin: @vw10 0 @vw30 0;
        }
      }
      .bigTitle {
        display: block;
        .transition(.3s);
      }
      .arrow {
        display: block;
        width: @vw112 + (@vw16 * 2);
        height: @vw15;
        position: relative;
        margin: @vw10 auto 0 auto;
        &:before {
          content: '';
          background: @hardBlack;
          height: 2px;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          position: absolute;
          width: 100%;
          .transition(.3s);
        }
        &:after {
          content: '';
          height: @vw15;
          width: @vw15;
          border-top: 2px solid @hardBlack;
          border-right: 2px solid @hardBlack;
          right: 0;
          top: 50%;
          transform: translateY(-50%) rotate(45deg);
          position: absolute;
          .transition(.3s);
        }
      }
      .imageWrapper {
        display: block;
        max-width: 100%;
        width: (@vw112 * 3) + (@vw16 * 4);
        overflow: hidden;
        height: auto;
        position: relative;
        border-radius: @vw20;
        .innerImage {
          margin: auto;
          height: 0;
          display: block;
          padding-bottom: 137.5%;
          img, video {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            transform: translate(-50%,-50%) scale(1);
            .transition(.3s);
          }
        }
      }
      .textLink {
        display: inline-block;
      }
    }
  }
}

@media screen and (max-width: 1080px) {
  .menusBlock {
    .sliderIndicator {
      margin-top: @vw50-1080;
      width: (@vw112-1080 * 3) + (@vw16-1080 * 3);
      border-radius: @vw10-1080;
    }
    .menus {
      margin-left: -@vw16-1080;
      width: calc(100% ~"+" @vw32-1080);
      .bigLogoWrapper {
        width: (@vw112-1080 * 4) + (@vw16-1080 * 3);
      }
      .menu {
        margin: 0 @vw16-1080;
        width: calc(33.3333% ~"-" @vw32-1080);
        transform: translateY(@vw20-1080);
        &:nth-child(even) {
          .bigTitle {
            margin: @vw30-1080 0 @vw10-1080 0;
          }
        }
        &:nth-child(odd) {
          .textLink {
            margin: @vw10-1080 0 @vw30-1080 0;
          }
        }
        .arrow {
          width: @vw112-1080 + (@vw16-1080 * 2);
          height: @vw15-1080;
          margin: @vw10-1080 auto 0 auto;
          &:after {
            height: @vw15-1080;
            width: @vw15-1080;
          }
        }
        .imageWrapper {
          width: (@vw112-1080 * 3) + (@vw16-1080 * 4);
          border-radius: @vw20-1080;
          .innerImage {
            padding-bottom: 100%;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .menusBlock {
    .sliderButton {
      top: 50%;
      &.next {
        transform: translateY(-50%) translateX(50%) rotate(-90deg);
      }
      &.prev {
        transform: translateY(-50%) translateX(-50%) rotate(90deg);
      }
    }
    &:has(.slider) {
      .menus {
        display: block;
        width: 100%;
        margin-left: 0;
        .menu {
          display: inline-flex;
          &:not(:last-child) {
            margin-bottom: 0;
          }
        }
      }
    }
    .sliderIndicator {
      margin-top: @vw50-580;
      width: (@vw112-580 * 3) + (@vw16-580 * 3);
      border-radius: @vw10-580;
    }
    .menus {
      margin-left: -@vw16-580;
      width: calc(100% ~"+" @vw32-580);
      .bigLogoWrapper {
        bottom: 0;
        width: (@vw112-580 * 6) + (@vw16-580 * 5);
      }
      .menu {
        margin: 0 @vw16-580;
        flex-direction: column-reverse;
        width: calc(100% ~"-" @vw32-580);
        transform: translateY(@vw20-580);
        &:not(:last-child) {
          margin-bottom: @vw40-580;
        }
        &:nth-child(even) {
          .bigTitle {
            margin: @vw30-580 0 @vw10-580 0;
          }
          .textLink {
            margin: @vw10-580 0 @vw30-580 0;
          }
        }
        &:nth-child(odd) {
          .bigTitle {
            margin: @vw30-580 0 @vw10-580 0;
          }
          .textLink {
            margin: @vw10-580 0 @vw30-580 0;
          }
        }
        .arrow {
          width: @vw112-580 + (@vw16-580 * 2);
          height: @vw15-580;
          margin: @vw10-580 auto 0 auto;
          &:after {
            height: @vw15-580;
            width: @vw15-580;
          }
        }
        .cols {
          padding: 0 @vw100-580;
        }
        .imageWrapper {
          width: (@vw112-580 * 3) + (@vw16-580 * 4);
          border-radius: @vw20-580;
        }
      }
    }
  }
}
