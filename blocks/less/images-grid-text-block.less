.imagesGridTextBlock {
  &.inview {
    .subTitle, .text, .buttonWrapper {
      opacity: 1;
      transform: translateY(0);
    }
  }
  .subTitle, .text, .buttonWrapper {
    opacity: 0;
    transform: translateY(@vw20);
    .transition(.45s .45s);
  }
  .bigTitle {
    margin: @vw20 0 @vw40 0;
  }
  .buttonWrapper {
    display: block;
    margin-top: @vw60;
  }
  .cols {
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .col {
      display: inline-block;
      vertical-align: middle;
      margin: 0 @vw8;
      width: calc(50% ~"-" @vw16);
      &:last-child {
        padding: 0 @vw112 + @vw16;
      }
    }
  }
  .images {
    height: @vw100 * 7.8;
    position: relative;
  }
  .imageItem {
    max-width: 100%;
    height: auto;
    position: absolute;
    overflow: hidden;
    &:nth-child(1) {
      width: (@vw100 * 3) + @vw5;
      top: 0;
      left: 0;
      .innerImage {
        padding-bottom: 140.32786885245903%;
      }
    }
    &:nth-child(2) {
      width: (@vw112 * 2) + (@vw16 * 2);
      right: @vw112;
      top: @vw55;
      .innerImage {
        padding-bottom: 83.984375%;
      }
    }
    &:nth-child(3) {
      width: @vw112 + (@vw16 * 2);
      left: @vw112;
      bottom: @vw88;
      .innerImage {
        padding-bottom: 159.72222222222223%;
      }
    }
    &:nth-child(4) {
      width: (@vw112 * 3) + (@vw16 * 3);
      right: 0;
      bottom: 0;
      .innerImage {
        padding-bottom: 123.82198952879581%;
      }
    }
    &.inview {
      .innerImage {
        .innerMask {
          height: 100%;
          width: 100%;
          transition: width 0.8s cubic-bezier(0.83, 0, 0.17, 1),
                      height 0.8s cubic-bezier(0.83, 0, 0.17, 1);

          img {
            transform: scale(1);
            transition: transform 0.8s cubic-bezier(0.83, 0, 0.17, 1);
          }
        }
      }
    }

    .innerImage {
      height: 0;
      padding-bottom: 100%;
      position: relative;  /* Added to ensure proper layout */

      .innerMask {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        border-radius: @vw20;
        overflow: hidden;
        width: 0;
        height: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        transition: width 0.8s cubic-bezier(0.83, 0, 0.17, 1),
                    height 0.8s cubic-bezier(0.83, 0, 0.17, 1);

        img {
          position: absolute;
          object-fit: cover;
          object-position: center;
          margin: auto;
          display: block;
          transform: scale(2);
          transition: transform 0.8s cubic-bezier(0.83, 0, 0.17, 1);
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

@media screen and (max-width: 1080px) {
  .imagesGridTextBlock {
    .subTitle, .text, .buttonWrapper {
      transform: translateY(@vw20-1080);
    }
    .bigTitle {
      margin: @vw20-1080 0 @vw40-1080 0;
    }
    .buttonWrapper {
      margin-top: @vw60-1080;
    }
    .cols {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .col {
        margin: 0 @vw8-1080;
        width: calc(50% ~"-" @vw16-1080);
        &:last-child {
          padding: 0 @vw112-1080 + @vw16-1080;
          padding-right: 0;
        }
      }
    }
    .images {
      height: @vw100-1080 * 7;
    }
    .imageItem {
      &:nth-child(1) {
        width: (@vw100-1080 * 2) + @vw5-1080;
      }
      &:nth-child(2) {
        width: (@vw112-1080 * 2) + (@vw16-1080 * 2);
        right: 0;
        top: @vw55-1080;
      }
      &:nth-child(3) {
        width: @vw112-1080 + (@vw16-1080 * 2);
        left: @vw50-1080;
        bottom: @vw88-1080;
      }
      &:nth-child(4) {
        width: (@vw112-1080 * 2) + (@vw16-1080 * 3);
      }
      .innerImage {
        .innerMask {
          border-radius: @vw20-1080;
        }
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .imagesGridTextBlock {
    .subTitle, .text, .buttonWrapper {
      transform: translateY(@vw20-580);
    }
    .bigTitle {
      margin: @vw20-580 0 @vw40-580 0;
    }
    .buttonWrapper {
      margin-top: @vw60-580;
    }
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &:last-child {
          margin-top: @vw40-580;
          padding: 0;
        }
      }
    }
    .images {
      height: @vw100-580 * 6;
    }
    .imageItem {
      &:nth-child(1) {
        width: (@vw100-580 * 2) + @vw5-580;
      }
      &:nth-child(2) {
        width: (@vw112-580 * 2) + (@vw16-580 * 1);
        right: @vw22-580;
        top: @vw55-580;
      }
      &:nth-child(3) {
        width: @vw112-580 + (@vw16-580 * 2);
        left: @vw50-580;
        bottom: @vw50-580;
      }
      &:nth-child(4) {
        width: (@vw112-580 * 2) + (@vw16-580 * 2);
      }
      .innerImage {
        .innerMask {
          border-radius: @vw20-580;
        }
      }
    }
  }
}
