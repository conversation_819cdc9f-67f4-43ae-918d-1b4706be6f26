// out: false
.stickyBigMediaBlock {
    .contentWrapper {
        height: 100%;
        position: relative;
    }
    .mediaWrapper {
        position: relative;
        margin: auto;
        display: block;
        overflow: hidden;
        width: 100%;
        // width: calc(100% ~"-" @vw200 ~"-" @vw200 ~"-" @vw20 ~"-" @vw20); 
        height: auto;
        .innerImage {
            position: relative;
            top: 0;
            left: 0;
            width: 100%;
            .paddingRatio(1302,819);
            height: 0;
            .item {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                opacity: 0;
                .transitionMore(opacity, .3s);
                transition-delay: .15s;
                &.active {
                    opacity: 1;
                    transition-delay: 0s;
                }
            }
            img, video {
                position: absolute;
                top: 50%;
                left: 50%;
                .transform(translate(-50%, -50%));
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
            }
        }
    }

    // Opposite/inverted animation styles
    &.opposite {
        .mediaWrapper {
            width: 100%; // Start at full width for opposite animation
        }
    }
}

@media all and (max-width: 1080px) {
    .stickyBigMediaBlock {
        .mediaWrapper {
            width: calc(100% ~"-" @vw100-1080 * 2);
        }
    }
}

@media all and (max-width: 580px) {
    .stickyBigMediaBlock {
        .mediaWrapper {
            width: calc(100% ~"-" @vw100-580 * 2);
            .innerImage {
                .paddingRatio(10,16);
            }
        }
    }
}