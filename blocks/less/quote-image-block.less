// out: false
.quoteImageBlock {
  &.inview {
    .signature {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .normalTitle {
    padding-right: @vw40;
  }
  .signature {
    display: block;
    opacity: 0;
    transform: translateY(@vw20);
    margin-top: @vw40;
  }
  .col {
    display: inline-block;
    vertical-align: middle;
    width: (@vw112 * 7) + (@vw16 * 6);
    &:not(.image) {
      &:first-child {
        width: (@vw112 * 6) + (@vw16 * 5);
      }
    }
    &.image {
      width: (@vw112 * 5) + (@vw16 * 5);
      &:last-child {
        width: (@vw112 * 4) + (@vw16 * 4);
        .imageWrapper {
          left: -@vw112;
          width: calc(100% ~"+" @vw112);
          -webkit-mask-image: linear-gradient(-90deg, rgba(0,0,0,1), rgba(0,0,0,0));
          mask-image: linear-gradient(-90deg, rgba(0,0,0,1), rgba(0,0,0,0));
          .innerImage {
            .padding-bottom-generator(624, 728);
          }
        }
      }
      .imageWrapper {
        height: auto;
        border-radius: @vw20;
        overflow: hidden;
        position: relative;
        width: calc(100% ~"+" (@vw112 * 2) ~"+" @vw16);
        -webkit-mask-image: linear-gradient(90deg, rgba(0,0,0,1), rgba(0,0,0,0));
        mask-image: linear-gradient(90deg, rgba(0,0,0,1), rgba(0,0,0,0));
        .innerImage {
          height: 0;
          .padding-bottom-generator(880, 728);
          img {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            object-fit: cover;
            object-position: center;
          }
        }
      }
    }
    .innerCol {
      &:not(:last-child) {
        margin-bottom: @vw30;
      }
    }
  }
}
@media all and (max-width: 1080px) {
  .quoteImageBlock {
    .normalTitle {
      padding-right: @vw40-1080;
    }
    .signature {
      transform: translateY(@vw20-1080);
      margin-top: @vw40-1080;
    }
    .col {
      width: (@vw112-1080 * 5) + (@vw16-1080 * 5);
      &:not(.image) {
        &:first-child {
          width: (@vw112-1080 * 5) + (@vw16-1080 * 5);
        }
      }
      &.image {
        width: (@vw112-1080 * 3) + (@vw16-1080 * 4);
        &:last-child {
          width: (@vw112-1080 * 3) + (@vw16-1080 * 4);
          .imageWrapper {
            left: -@vw112-1080;
            width: calc(100% ~"+" @vw112-1080);
          }
        }
        .imageWrapper {
          border-radius: @vw20-1080;
          width: calc(100% ~"+" (@vw112-1080 * 2) ~"+" @vw16-1080);
        }
      }
      .innerCol {
        &:not(:last-child) {
          margin-bottom: @vw30-1080;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .quoteImageBlock {
    .normalTitle {
      padding-right: @vw40-580;
    }
    .signature {
      transform: translateY(@vw20-580);
      margin-top: @vw40-580;
    }
    .col {
      width: 100%;
      .transform(translate3d(0,0,0)) !important;
      &:not(:last-child) {
        margin-bottom: @vw40-580;
      }
      &:not(.image) {
        &:first-child {
          width: 100%;
        }
      }
      &.image {
        width: 100%;
        &:last-child {
          width: 100%;
          .imageWrapper {
            left: 0;
            width: 100%;
            -webkit-mask-image: none;
            mask-image: none;
          }
        }
        .imageWrapper {
          border-radius: @vw20-580;
          width: 100%;
          -webkit-mask-image: linear-gradient(rgba(0,0,0,1), rgba(0,0,0,0));
          mask-image: linear-gradient(rgba(0,0,0,1), rgba(0,0,0,0));
        }
      }
      .innerCol {
        &:not(:last-child) {
          margin-bottom: @vw30-580;
        }
      }
    }
  }
}
