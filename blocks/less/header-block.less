// out: false
.headerBlock {
  margin-top: 0;
  opacity: 0;
  height: 100vh;
  min-height: @vw100 * 6;
  &.inview {
    opacity: 1;
    .transitionMore(opacity, .6s, .45s);
  }
  .backgroundImage {
    position: relative;
    overflow: hidden;
    height: auto;
    vertical-align: top;
    width: 100%;
    height: 100%;
    .transform(translate3d(0,0,0));
    .innerImage {
      height: 100%;
      width: 100%;
      .innerTransform {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
      img, video {
        position: absolute;
        object-fit: cover;
        object-position: center;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
  }
}
