// out: false
.roomWrapper {
  &:first-child {
    .bigHeaderBlock {
      height: 100vh;
      min-height: @vw100 * 6;
      .backgroundImage {
       width: 100%;
       height: 100%;
       .innerImage {
          height: 100%;
          width: 100%;
          padding-bottom: 0;
        }
      }
    }
  }
}
body {
  &:has(:not(.roomWrapper)) {
    .bigHeaderBlock {
      height: 100vh;
      min-height: @vw100 * 6;
      .backgroundImage {
       width: 100%;
       height: 100%;
       .innerImage {
          height: 100%;
          width: 100%;
          padding-bottom: 0;
        }
      }
    }
  }
}
.bigHeaderBlock {
  margin-top: 0;
  &.inview {
    .backgroundImage {
      .innerImage {
        .innerTransform {
          opacity: 1;
          .transitionMore(opacity, 0.45s, 0s, ease-in-out);
        }
      }
    }
    .titleWrapper {
      .filter(blur(0));
      opacity: 1;
      transition: filter 0.45s .6s ease-in-out, opacity 0.45s .6s ease-in-out;
    }
  }
  .backgroundImage {
    position: relative;
    overflow: hidden;
    height: auto;
    left: 0;
    right: 0;
    margin: auto;
    vertical-align: top;
    width: calc(100% ~"-" @vw90 ~"-" @vw90);
    .transform(translate3d(0,0,0));
    .innerImage {
      .paddingRatio(1920, 926);
      height: 0;
      width: 100%;
      .innerTransform {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        opacity: 0;
        height: 100%;
      }
      img, video {
        position: absolute;
        object-fit: cover;
        object-position: center;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
  }
  .titleWrapper {
    position: absolute;
    top: auto;
    left: 0;
    bottom: @vw50;
    width: 100%;
    .filter(blur(10px));
    opacity: 0;
  }
}
