// out: false
.imagesTextBlock {
  &.inview {
    .titleItems, .textItems, .imageItems {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
    .divider {
      opacity: 1;
      .filter(blur(0px));
    }
  }
  .contentWrapper {
    height: 100%;
  }
  .stickyWrapper {
    height: 100%;
    position: relative;
    .stickyCols {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: auto;
    }
  }
  .cols {
    margin-left: -@vw10;
    width: calc(100% ~"+" @vw20);
    .col {
      display: inline-block;
      vertical-align: middle;
      margin: 0 @vw10;
      width: calc(50% ~"-" @vw20);
      &.text {
        padding-left: @vw70;
      }
    }
  }
  .titleItems, .textItems, .imageItems {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .imageWrapper {
    height: auto;
    position: relative;
    overflow: hidden;
    width: 100%;
    .innerImage {
      height: 0;
      .paddingRatio(640, 719);
      .item {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        .filter(blur(10px));
        transition: filter 0.6s, opacity 0.6s;
        &.active {
          opacity: 1;
          .filter(blur(0px));
          transition-delay: 0.3s;
        }
      }
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .divider {
    .transform(scaleX(0));
    height: 2px;
    width: 100%;
    background: @primaryColor;
    margin-top: @vw30;
    transform-origin: left;
    opacity: 0;
    .filter(blur(10px));
    transition: opacity 0.6s 0.6s, filter 0.6s 0.6s;
    -webkit-transition: opacity 0.6s 0.6s, -webkit-filter 0.6s 0.6s;
  }
  .titleItems, .textItems {
    position: relative;
    .item {
      position: absolute;
      top: 0;
      left: 0;
      .filter(blur(20px));
      opacity: 0;
      .transitionMore(all, .6s, .0s, ease-in-out);
      &.active {
        opacity: 1;
        .filter(blur(0px));
      }
    }
  }
  .titleItems {
    .item {
      bottom: 0;
      top: auto;
    }
  }
  .textItems {
    .item {
      .transitionMore(all, .6s, .15s, ease-in-out);
    }
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .text {
    margin-top: @vw30;
  }
}
@media all and (max-width: 1080px) {
  .imageTextBlock {
    .cols {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .col {
        margin: 0 @vw8-1080;
        width: calc(50% ~"-" @vw16-1080);
        &.text {
          padding: 0 @vw40-1080;
        }
        &:not(.text) {
          &:first-child {
            padding-left: @vw50-1080;
            .imageWrapper {
              .innerImage {
                // .paddingRatio(624, 528);
              }
            }
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-1080);
    }
    .subTitle {
      margin-bottom: @vw20-1080;
    }
    .text {
      margin-top: @vw30-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .imageTextBlock {
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &.text {
          padding: 0;
        }
        &:not(:last-child){
          margin-bottom: @vw40-580;
        }
        &:not(.text) {
          &:first-child {
            padding-left: 0;
            .imageWrapper {
              .innerImage {
                // .paddingRatio(624, 528);
              }
            }
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-580);
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .text {
      margin-top: @vw30-580;
    }
  }
}
