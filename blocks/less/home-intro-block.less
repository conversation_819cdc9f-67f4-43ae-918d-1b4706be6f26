// out: false
.homeIntroBlock {
  &.inview {
    .contentWrapper {
      .cols {
        .col {
          .roundButton {
            opacity: 1;
            transform: scale(1);
            transition-delay: .9s;
          }
        }
      }
    }
  }
  .contentWrapper {
    .cols {
      display: flex;
    }
    .col {
      position: relative;
      display: inline-block;
      vertical-align: middle;
      &:first-child {
        height: @vw100 * 9;
        .imageItem {
          &:first-child {
            width: calc(100% ~"-" @vw16);
            top: 0;
            left: 0;
          }
          &:last-child {
            width: (@vw112 * 2) + (@vw16 * 2);
            bottom: 0;
            right: 0;
          }
        }
      }
      &:last-child {
        .imageItem {
          &:first-child {
            right: 0;
            top: @vw100 + @vw50;
            width: (@vw112 * 2) + (@vw16 * 1);
            .innerImage {
              padding-bottom: 165.41666666666669%;
            }
          }
          &:last-child {
            bottom: @vw70;
            left: @vw16 + @vw16;
            width: (@vw112 * 2) + (@vw16 * 2);
          }
        }
      }
      &.small {
        width: (@vw112 * 3) + (@vw16 * 3);
      }
      &.big {
        display: inline-flex;
        justify-content: center;
        align-items: center;
        gap: @vw20;
        flex-wrap: wrap;
        flex-direction: column;
        width: (@vw112 * 6) + (@vw16 * 5);
        .bigTitle {
          &:nth-child(2) {
            .wrapper {
              &:first-child {
                margin-bottom: @vw20;
              }
            }
          }
        }
      }
    }
    .roundButton {
      position: absolute;
      top: 0;
      left: 0;
      opacity: 0;
      transform: scale(0);
      .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.34, 1.56, 0.64, 1));
      &.mobile {
        display: none;
        position: relative;
      }
    }
    .bigTitle {
      text-align: center;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      span {
        display: inline-block;
        margin-bottom: -@vw10;
        vertical-align: middle;
        &:not(:last-child){
          padding: 0 @vw5;
        }
        &:first-child {
          padding-left: 0;
        }
        &:last-child {
          padding-right: 0;
        }
      }
    }
    .subTitleWrapper {
      padding: @vw10 0;
      position: relative;
      &:after {
        content: '';
        background: @primaryColor;
        height: 1px;
        bottom: 0;
        left: 0;
        width: 0%;
        .transitionMore(width, 0.6s, 0s, cubic-bezier(0.83, 0, 0.17, 1));
        transition-delay: .15s;
        position: absolute;
      }
      &.inview {
        &:after {
          width: 100%;
        }
        i {
          transform: translate(100%,-100%) scale(1);
        }
        .subTitle {
          opacity: 1;
          transform: translateY(0);
        }
      }
      i {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(100%,-100%) scale(0);
        color: @primaryColor;
        font-size: @vw12;
        .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.34, 1.56, 0.64, 1));
      }
      .subTitle {
        opacity: 0;
        transform: translateY(@vw20);
        .transition(.3s);
      }
    }
    .signature {
      opacity: 0;
      .transition(.3s);
      &.inview {
        opacity: 1;
      }
    }
  }
}

@media all and (max-width: 1080px) {
  .homeIntroBlock {
    .contentWrapper {
      .cols {
        .col {
          &:first-child {
            height: @vw100-1080 * 6;
            .imageItem {
              &:first-child {
                width: calc(100% ~"-" @vw100-1080);
              }
              &:last-child {
                right: @vw40-1080;
                bottom: @vw40-1080;
                width: @vw100-1080 + @vw40-1080 + (@vw16-1080 * 2);
              }
            }
          }
          &:last-child {
            .imageItem {
              &:first-child {
                top: @vw50-1080;
                width: @vw112-1080 + @vw40-1080 + (@vw16-1080 * 2);
              }
              &:last-child {
                bottom: 0;
                left: @vw16-1080 + @vw16-1080;
                width: @vw100-1080 + @vw40-1080 + (@vw16-1080 * 2);
              }
            }
          }
          &.small {
            width: calc(100% ~"-" (@vw112-1080 * 4) + (@vw16-1080 * 3.5));
          }
          &.big {
            padding: 0 @vw30-1080;
            gap: @vw20-1080;
            width: (@vw112-1080 * 8) + (@vw16-1080 * 7);
            .bigTitle {
              &:nth-child(2) {
                .wrapper {
                  &:first-child {
                    margin-bottom: @vw20-1080;
                  }
                }
              }
            }
          }
        }
      }
      .roundButton {
        .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.34, 1.56, 0.64, 1));
      }
      .bigTitle {
        span {
          margin-bottom: -@vw10-1080;
          &:not(:last-child) {
            padding: 0 @vw5-1080;
          }
        }
      }
      .subTitleWrapper {
        padding: @vw10-1080 0;
        i {
          font-size: @vw12-1080;
        }
        .subTitle {
          transform: translateY(@vw20-1080);
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .homeIntroBlock {
    .contentWrapper {
      padding: 0;
      .imageItem {
        max-width: none;
      }
      .cols {
        .col {
          &:first-child {
            height: @vw100-580 * 8;
            .imageItem {
              &:first-child {
                left: auto;
                right: 0;
                top: @vw40-580;
                width: @vw112-580 + @vw112-580;
                transform: translateX(0);
              }
              &:last-child {
                right: 0;
                bottom: @vw40-580;
                width: @vw112-580 + (@vw16-580 * 2);
              }
            }
          }
          &:last-child {
            .imageItem {
              &:first-child {
                left: @vw14-580;
                right: auto;
                top: 0;
                width: @vw112-580 + @vw40-580 + (@vw16-580 * 2);
              }
              &:last-child {
                bottom: 0;
                left: 0;
                width: (@vw100-580 * 1) + @vw40-580 + (@vw16-580 * 2);
              }
            }
          }
          &.small {
            width: calc(100% ~"-" (@vw112-580 * 3) + (@vw16-580 * 3));
          }
          &.big {
            padding: 0 @vw20-580;
            gap: @vw20-580;
            width: (@vw112-580 * 6) + (@vw16-580 * 6);
            .bigTitle {
              &:nth-child(2) {
                .wrapper {
                  &:first-child { 
                    margin-bottom: 0;
                  }
                  &:nth-child(2) {
                    margin-bottom: @vw20-580;
                  }
                }
              }
            }
          }
        }
      }
      .roundButton {
        display: none;
        .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.34, 1.56, 0.64, 1));
        &.mobile {
          margin-top: @vw40-580;
          display: block;
        }
        .svgWrapper {
          left: 0;
          top: 0;
        }
        .innerText {
          position: absolute;
          display: inline-block;
          left: 0;
          right: 0;
          top: 50%;
          transforM: translateY(-50%);
        }
      }
      .bigTitle {
        span {
          margin-bottom: 0;
          &:not(:last-child) {
            padding: 0 @vw5-580;
          }
        }
      }
      .subTitleWrapper {
        padding: @vw10-580 0;
        i {
          font-size: @vw16-580;
        }
        .subTitle {
          transform: translateY(@vw20-580);
        }
      }
    }
  }
}
