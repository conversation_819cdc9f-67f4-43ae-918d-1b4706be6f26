// out: false
.staffSliderBlock {
  .innerContent {
    width: (@vw112 * 5) + (@vw16 * 4);
    .text {
      margin: @vw60 0;
    }
  }
  .contentWrapper {
    position: relative;
  }
  .wrapper, .sliderWrapper {
    position: relative;
  }
  .roundButton {
    position: absolute;
    top: 0;
    color: @hardWhite;
    right: 0;
  }
  .slider {
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .slide {
      display: inline-block;
      margin: 0 @vw8;
      width: calc(25% ~"-" @vw16);
      .imageWrapper {
        margin-bottom: @vw20;
        width: 100%;
        position: relative;
        overflow: hidden;
        border-radius: @vw20;
        height: auto;
        .innerImage {
          .padding-bottom-generator(304,430);
          height: 0;
          width: 100%;
          position: relative;
          img {
            position: absolute;
            top: 0;
            left: 0;
            widht: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            pointer-events: none;
          }
        }
      }
    }
  }
  .sliderButton {
    position: absolute;
    top: 50%;
    &.next {
      right: 0;
      transform: translateY(-50%) translateX(50%) rotate(-90deg);
    }
    &.prev {
      left: 0;
      transform: translateY(-50%) translateX(-50%) rotate(90deg);
    }
  }
  .sliderIndicator {
    margin-top: @vw50;
    width: (@vw112 * 3) + (@vw16 * 3);
    overflow: hidden;
    height: 2px;
    position: relative;
    background: rgba(255,255,255,.2);
    .innerBar {
      background: @primaryColor;
      border-radius: @vw10;
      height: 100%;
      width: 0;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
}
@media all and (max-width: 1080px) {
  .staffSliderBlock {
    .innerContent {
      width: (@vw112-1080 * 5) + (@vw16-1080 * 4);
      .text {
        margin: @vw60-1080 0;
      }
    }
    .slider {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .slide {
        margin: 0 @vw8-1080;
        width: calc(25% ~"-" @vw16-1080);
        .imageWrapper {
          margin-bottom: @vw20-1080;
          border-radius: @vw20-1080;
        }
      }
    }
    .sliderButton {
      top: 50%;
      &.next {
        transform: translateY(-50%) translateX(50%) rotate(-90deg);
      }
      &.prev {
        transform: translateY(-50%) translateX(-50%) rotate(90deg);
      }
    }
    .sliderIndicator {
      margin-top: @vw50-1080;
      width: (@vw112-1080 * 3) + (@vw16-1080 * 3);
      border-radius: @vw10-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .staffSliderBlock {
    .innerContent {
      width: 100%;
      .text {
        margin: @vw60-580 0;
      }
    }
    .bigTitle {
      width: 60%;
    }
    .slider {
      margin-left: 0;
      width: 100%;
      .slide {
        margin: 0 @vw8-580;
        width: calc(50% ~"-" @vw16-580);
        .imageWrapper {
          margin-bottom: @vw20-580;
          border-radius: @vw20-580;
        }
      }
    }
    .sliderButton {
      top: 50%;
      &.next {
        transform: translateY(-50%) translateX(50%) rotate(-90deg);
      }
      &.prev {
        transform: translateY(-50%) translateX(-50%) rotate(90deg);
      }
    }
    .sliderIndicator {
      margin-top: @vw50-580;
      width: (@vw112-580 * 3) + (@vw16-580 * 3);
      border-radius: @vw10-580;
    }
  }
}
