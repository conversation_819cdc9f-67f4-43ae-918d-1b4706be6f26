// out : false
.faqBlock {
  &.inview {
    .bigTitle, .text, .faqItems {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .cols {
    margin-left: -@vw10;
    width: calc(100% ~"+" @vw20);
    .col {
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw10;
      width: calc(50% ~"-" @vw20);
      &.textCol {
        padding-right: @vw200 + @vw20;
      }
    }
  }
  .bigTitle, .text, .faqItems {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .bigTitle {
    margin-bottom: @vw40;
  }
  .text {
    margin-top: @vw30;
  }
  .faqItems {
    .faqItem {
      position: relative;
      padding-bottom: @vw30;
      cursor: pointer;
      &.active {
        .question {
          &:after {
            .transform(translateY(-50%) rotate(0deg));
          }
        }
        .answer {
          max-height: 1000px;
        }
      }
      &:after {
        content: '';
        position: absolute;
        height: 2px;
        background: @almostBlack;
        width: 100%;
        left: 0;
        bottom: 0;
      }
      * {
        pointer-events: none;
      }
      .question {
        font-weight: bold;
        padding-top: @vw20;
        padding-right: @vw70;
        position: relative;
        &:before, &:after {
          content: '';
          position: absolute;
          height: 2px;
          width: @vw20;
          background: @almostBlack;
          top: 70%;
          left: auto;
          right: 0;
          .transform(translateY(-50%));
          .transitionMore(transform, 0.6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
        }
        &:after {
          .transform(translateY(-50%) rotate(90deg));
        }
        .subTitle {
          // semibold font weight
          font-weight: 600; 
        }
      }
      .answer {
        max-height: 0;
        overflow: hidden;
        .transition(.3s);
        .text {
          margin-top: @vw30;
        }
      }
      .text {
        margin-top: 0;
      }
    }
  }
} 