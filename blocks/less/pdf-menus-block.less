// out: false
body {
  &:has(.pdfMenusBlock) {
    #header {
      background-color: @almostBlack;
      color: @almostWhite;
      .logo {
        svg {
          path, rect {
            fill: @almostWhite;
          }
        }
      }
      .hamburger {
        border-color: @almostWhite;
        .border {
          background: @almostWhite;
        }
      }
      .button {
        color: @almostWhite;
        border-color: @almostWhite;
        &:hover {
          color: @almostBlack;
          background: @almostWhite;
        }
      }
    }
    #pageContainer {
      background: @almostBlack;
    }
    footer, .whiteSpaceWrapper {
      display: none;
    }
  }
}
.pdfMenusBlock {
  background: @almostBlack;
  margin-top: 0;
  padding-top: @vw100 + @vw80;
  color: @almostWhite;
  opacity: 0;
  &.inview {
    opacity: 1;
    .transitionMore(opacity, .6s, .3s, ease-in-out);
  }
  .filterButtons, .downloadSection {
    display: inline-block;
    vertical-align: middle;
    width: 50%;
  }
  .downloadSection {
    text-align: right;
    .downloadBtn {
      border: none;
      cursor: pointer;
      display: inline-block;
      background: transparent;
      color: @almostWhite;
      .transitionMore(opacity, .3s);
      &:hover {
        opacity: .4;
      }
      * {
        pointer-events: none;
      }
    }
    i {
      font-size: @vw22;
      margin-right: @vw10;
    }
  }
  .menuItem {
    display: inline-block;
    position: relative;
    .transitionMore(color, .3s);
    &.active {
      color: @primaryColor;
    }
    button {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      cursor: pointer;
    }
    &:not(:last-child) {
      margin-right: @vw30;
    }
  }
  .pdfFrame {
    margin-top: @vw50;
    height: 100lvh;
    width: 100%;
  }
}