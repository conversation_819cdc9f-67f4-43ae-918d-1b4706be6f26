// out: false
.articlesBlock {
  position: relative;
  &.inview {
    .slider {
      visibility: visible;
    }
    .cols {
      &:before {
        .transform(scaleX(1));
        .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.85, 0, 0.15, 1));
      }
    }
  }
  .slider {
    overflow: hidden;
    position: relative;
    white-space: nowrap;
    visibility: hidden;
    .slide {
      margin-right: @vw20;
      width: calc(60% ~"-" @vw40);
      position: relative;
      &.is-selected {
        .innerContent {
          .transform(translateY(0));
        }
        &:hover {
          .innerImage {
            img {
              .transform(scale(1.05));
            }
          }
        }
      }
      .innerLink {
        overflow: hidden;
        position: relative;
        .transform(translate3d(0,0,0));
        top: 0;
        display: inline-block;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        color: @almostWhite;
      }
      .innerContent {
        background: @almostBlack;
        padding: @vw30 @vw20;
        line-height: 1;
        color: @almostWhite;
        position: absolute;
        width: 100%;
        height: auto;
        left: 0;
        bottom: 0;
        .transform(translateY(100%));
        .transitionMore(transform, 0.6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
        .subTitle {
          line-height: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
  .arrows {
    margin-top: @vw80;
    .sliderButton {
      display: inline-block;
      &:not(:last-child) {
        margin-right: @vw20;
      }
    }
  }
  .contentWrapper {
    height: 100%;
  }
  .cols {
    position: relative;
    margin-left: -@vw10;
    padding-top: @vw100 + @vw20;
    width: calc(100% ~"+" @vw20);
    &:before {
      content: '';
      .transform(scaleX(0));
      height: 1px;
      position: absolute;
      top: 0;
      width: 100%;
      background: @almostBlack;
      transform-origin: left;
    }
    .col {
      display: inline-block;
      vertical-align: middle;
      margin: 0 @vw10;
      width: calc(60% ~"-" @vw20);
      &.text {
        width: calc(40% ~"-" @vw20);
        padding-right: @vw200 + @vw20;
      }
    }
  }
  .imageWrapper {
    height: auto;
    position: relative;
    overflow: hidden;
    width: 100%;
    .innerImage {
      height: 0;
      .paddingRatio(1, 1);
      .item {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        .transitionMore(opacity, .45s, 0s, ease-in-out);
        &:not(:first-child) {
          clip-path: inset(100%);
          opacity: 1;
        }
        &.active {
          opacity: 1;
          // clip-path: inset(0 0 0 0);
        }
      }
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
        .transitionMore(transform, 0.3s, 0s, ease-in-out);
      }
    }
  }
  .text {
    margin-top: @vw30;
  }
}
@media all and (max-width: 1080px) {
  .imageTextBlock {
    .cols {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .col {
        margin: 0 @vw8-1080;
        width: calc(50% ~"-" @vw16-1080);
        &.text {
          padding: 0 @vw40-1080;
        }
        &:not(.text) {
          &:first-child {
            padding-left: @vw50-1080;
            .imageWrapper {
              .innerImage {
                // .paddingRatio(624, 528);
              }
            }
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-1080);
    }
    .subTitle {
      margin-bottom: @vw20-1080;
    }
    .text {
      margin-top: @vw30-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .imageTextBlock {
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &.text {
          padding: 0;
        }
        &:not(:last-child){
          margin-bottom: @vw40-580;
        }
        &:not(.text) {
          &:first-child {
            padding-left: 0;
            .imageWrapper {
              .innerImage {
                // .paddingRatio(624, 528);
              }
            }
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-580);
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .text {
      margin-top: @vw30-580;
    }
  }
}
