.dividerBlock {
  &.inview {
    .divider {
      .innerCol {
        &:nth-child(1), &:nth-child(3) {
          .innerBar {
            width: 100%;
          }
        }
        i {
          transform: scale(1);
        }
      }
    }
  }
  .divider {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    .innerCol {
      display: inline-block;
      position: relative;
      &:nth-child(1), &:nth-child(3) {
        width: 100%;
        .innerBar {
          background: @primaryColor;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 0;
          width: 0%;
          height: 1px;
          .transitionMore(width, 0.6s, .45s, cubic-bezier(0.83, 0, 0.17, 1));
        }
      }
      &:nth-child(1) {
        .innerBar {
          right: 0;
          left: auto;
          -webkit-mask-image: linear-gradient(-90deg, rgba(0,0,0,1), rgba(0,0,0,0));
          mask-image: linear-gradient(-90deg, rgba(0,0,0,1), rgba(0,0,0,0));
        }
      }
      &:nth-child(2) {
        text-align: center;
        width: @vw60 + @vw5;
      }
      &:nth-child(3) {
        .innerBar {
          -webkit-mask-image: linear-gradient(90deg, rgba(0,0,0,1), rgba(0,0,0,0));
          mask-image: linear-gradient(90deg, rgba(0,0,0,1), rgba(0,0,0,0));
        }
      }
      i {
        display: block;
        font-size: @vw16;
        transform: scale(0);
        color: @primaryColor;
        .transitionMore(transform, 0.6s, .6s, cubic-bezier(0.34, 1.56, 0.64, 1));
      }
    }
  }
}

@media screen and (max-width: 1080px) {
  .dividerBlock {
    .divider {
      .innerCol {
        i {
            font-size: @vw16-1080;
        }
      }
    }
    .divider {
      .innerCol {
        &:nth-child(2) {
          width: @vw60-1080 + @vw5-1080;
        }
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .dividerBlock {
    .divider {
      .innerCol {
        i {
          font-size: @vw30-580;
        }
      }
    }
    .divider {
      .innerCol {
        &:nth-child(2) {
          width: @vw100-580 + @vw5-580;
        }
      }
    }
  }
}
