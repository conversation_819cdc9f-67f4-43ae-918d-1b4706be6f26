.menuHightlightBlock {
  .imagesWrapper {
    height: 100vh;
    width: 100%;
    min-height: @vw100 * 6;
    .backgroundImage {
      border-radius: @vw20;
      overflow: hidden;
      width: 100%;
      height:100%;
      position: absolute;
      top: 0;
      left: 0;
      img {
        width: 100%;
        height:100%;
        position: absolute;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
      }
    }
    .menu {
      cursor: zoom-in;
      display: block;
      position: absolute;
      width: auto;
      transform: translate(-50%,-50%);
      left: 50%;
      top: 50%;
      height: calc(100% ~"-" @vw120);
      * {
        cursor: zoom-in;
      }
      &:hover {
        &:before {
          opacity: .8;
        }
        &:after {
          opacity: 1;
          transform: translate(-50%,-50%) rotate(0deg);
        }
      }
    }
  }
}
