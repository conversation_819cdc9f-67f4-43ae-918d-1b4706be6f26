// out: false
.partnersMarqueeBlock {
    .marqueeWrapper {
        position: relative;
    }
    .textTitle {
        text-align: center;
    }
    .marquee {
        white-space: nowrap;
        .itemsContainer {
            display: inline-block;
            overflow: hidden;
            position: relative;
            vertical-align: middle;
            .item {
                display: inline-block;
                vertical-align: middle;
                margin: 0 @vw50;
                a {
                    .transitionMore(opacity, .3s);
                    cursor: pointer;
                    &[title="Visit Traveler"] {
                        img {
                            height: @vw70;
                        }
                    }
                    &:hover {
                        opacity: .5;
                    }
                    * {
                        cursor: pointer;
                    }
                }
                img {
                    display: block;
                    width: auto;
                    height: @vw40;
                    object-fit: contain;
                }
            }
        }
    }
}

@media all and (max-width: 1080px) {
    .partnersMarqueeBlock {
        .marquee {
            .itemsContainer {
                .item {
                    margin: 0 @vw50-1080;
                    a {
                        &[title="Visit Traveler"] {
                            img {
                                height: @vw70-1080;
                            }
                        }
                    }
                    img {
                        height: @vw40-1080;
                    }
                }
            }
        }
    }
}

@media all and (max-width: 580px) {
    .partnersMarqueeBlock {
        .marquee {
            .itemsContainer {
                .item {
                    margin: 0 @vw30-580;
                    a {
                        &[title="Visit Traveler"] {
                            img {
                                height: @vw70-580;
                            }
                        }
                    }
                    img {
                        height: @vw50-580;
                    }
                }
            }
        }
    }
}
