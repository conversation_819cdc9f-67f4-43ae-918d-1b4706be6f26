// out: false
body {
  &:has(.contactBlock) {
    .footer, .whiteSpaceWrapper {
      display: none !important;
    }
    #header {
      background-color: @almostBlack;
      color: @almostWhite;
      .logo {
        svg {
          path, rect {
            fill: @almostWhite;
          }
        }
      }
      .hamburger {
        border-color: @almostWhite;
        .border {
          background: @almostWhite;
        }
      }
      .button {
        color: @almostWhite;
        border-color: @almostWhite;
        &:hover {
          color: @almostBlack;
          background: @almostWhite;
        }
      }
    }
    #pageContainer {
      background: @almostBlack;
    }
  }
}
.wpcf7-not-valid-tip, .wpcf7 form.invalid .wpcf7-response-output, .wpcf7 form.unaccepted .wpcf7-response-output, .wpcf7 form.payment-required .wpcf7-response-output, .wpcf7 form.sent .wpcf7-response-output {
  background: rgba(229, 93, 45, .3);
  border: none;
  color: @primaryColor;
  text-transform: none;
  padding: @vw14;
  margin-top: @vw10;
}
.wpcf7 form.sent .wpcf7-response-output {
  color: @almostWhite;
  background: rgba(245, 241, 234, .3);
}
.contactBlock {
  background: @almostBlack;
  margin-top: 0;
  margin-bottom: 0;
  padding-bottom: @vw100 + @vw80;
  color: @almostWhite;
  opacity: 0;
  &.inview {
    opacity: 1;
    .transitionMore(opacity, .6s, 0s, ease-in-out);
    .titleWrapper {
      opacity: 1;
      .filter(blur(0));
      .transitionMore(all, .6s, .9s, ease-in-out);
    }
    .backgroundImage {
      opacity: 1;
      .transitionMore(opacity, .6s, .15s, ease-in-out);
    }
  }
  label {
    color: @almostWhite;
    display: block;
    text-transform: uppercase;
    margin-bottom: @vw30;
    letter-spacing: .12rem;
    font-size: @vw18;
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .titleWrapper {
    position: absolute;
    bottom: @vw40;
    left: 0;
    opacity: 0;
    .filter(blur(10px));
    width: 100%;
    .hugeTitle {
      color: @almostWhite;
    }
  }
  .text {
    margin-top: @vw30;
  }
  .fluentform .ff-el-group {
    margin-bottom: 0;
  }
  .backgroundImage {
      width: 100%;
      position: relative;
      overflow: hidden;
      opacity: 0;
      height: auto;
      .innerImage {
        width: 100%;
        height: 0;
        .paddingRatio(1920,861);
        img, video {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
        }
      }
    }
    .tripAdvisor {
      display: inline-block;
      width: @vw65;
      height: @vw65;
      margin-top: @vw120;
      cursor: pointer;
      .transitionMore(opacity, .3s, 0s, ease-in-out);
      &:hover {
        opacity: .5;
      }
      img {
        pointer-events: none;
        width: 100%;
        height: auto;
      }
    }
    .socials {
      margin: @vw50 0 @vw20 0;
      .social {
        font-size: @vw33;
        &:not(:last-child) {
          margin-right: @vw30;
        }
      }
    }
    .innerCol {
      &:first-child {
        margin-bottom: @vw70;
      }
    }
  .contentWrapper {
    .cols {
      margin-top: @vw100 + @vw50;
      .col {
        vertical-align: top;
        display: inline-block;
        position: relative;
        width: 50%;
        &:first-child {
          padding-right: @vw100 + @vw66;
          &:before {
            content: '';
            width: 1px;
            height: 100%;
            background: @almostWhite;
            position: absolute;
            top: 0;
            right: @vw80;
          }
        }
        &:last-child {
          padding-left: @vw8;
        }

        .fluentform .ff-el-input--label.ff-el-is-required.asterisk-right label:after {
          color: @primaryColor;
        }
        
        .formWrapper {
          margin-top: @vw20;
          form {
            .field {
              margin: 0 @vw8;
              display: inline-block;
              width: calc(100% ~"-" @vw16);
              vertical-align: top;
              &.half {
                width: calc(50% ~"-" @vw16);
              }
              &.small {
                width: calc(25% ~"-" @vw16);
              }
              &.big {
                width: calc(75% ~"-" @vw16);
              }
            }

            select {
              appearance: none; /* Verbergt de standaard dropdown-pijl */
              -webkit-appearance: none;
              -moz-appearance: none;
              background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="gray"><path d="M5 7l5 5 5-5H5z"/></svg>');
              background-repeat: no-repeat;
              background-position: right 10px center;
              background-size: 16px;
              &.select2-hidden-accessible {
                width: 100%;
              }
            }

            input, select, textarea {
              width: 100%;
              padding: @vw16;
              border: 1px solid @almostWhite;
              .rounded(@vw10);
              font-size: @vw18;
              font-weight: 100;
              font-family: 'Figtree', Arial, sans-serif;
              box-sizing: border-box;
              background: transparent;
              margin-top: @vw20;
              letter-spacing: 0.1rem;
              color: @almostWhite;
              &:focus {
                border-color: none;
                outline: none;
              }

              &::-webkit-input-placeholder {
                color: rgba(@almostWhite, .7);
              }

              &:-moz-placeholder {
                color: rgba(@almostWhite, .7);
              }

              &::-moz-placeholder {
                color: rgba(@almostWhite, .7);
              }

              &:-ms-input-placeholder {
                color: rgba(@almostWhite, .7);
              }

              &::placeholder {
                color: rgba(@almostWhite, .7);
              }

            }

            select {
              color: rgba(@almostWhite, .7);
            }

            textarea {
              resize: none;
              height: @vw100 + @vw80;
            }

            input[type="submit"] {
              background-color: @almostWhite;
              font-weight: 500;
              height: @vw51;
              line-height: @vw51;
              color: @almostBlack;
              padding: 0 @vw50;
              display: block;
              width: auto;
              font-size: @vw18;
              .rounded(@vw10);
              cursor: pointer;
              .transition(.3s);
              -webkit-appearance: none;
              -moz-appearance: none;
              appearance: none;
            }
            .select2-container--default .select2-selection--single {
              height: @vw40;
              border: 1px solid #ddd;
              .rounded(@vw10);
            }

            .select2-container .select2-selection--single .select2-selection__rendered {
              padding-left: @vw10;
              line-height: 1;
            }

            .select2-container .select2-selection--single .select2-selection__arrow {
              height: @vw40;
              right: @vw10;
            }
            .error {
              color: #ff0000;
              background-color: #ffe6e6;
              border: 1px solid #ff0000;
              padding: 10px;
              .rounded(@vw10);
              margin-top: 5px;
              font-family: 'Figtree', Arial, sans-serif;
              font-size: @vw12;
              display: block;
            }
          }
        }
      }
    }
  }
  .link {
    color: @almostWhite;
    &:before, &:after {
      background: @almostWhite;
    }
  }
}

@media all and (max-width: 1080px) {
  .contactBlock {
    &.inview {
      .subTitle, .text, .imageWrapper {
        opacity: 1;
        transform: translateY(0);
        .transitionMore(all, .45s, .6s, ease-in-out);
      }
    }
    .imageWrapper {
      width: 100%;
      position: relative;
      overflow: hidden;
      height: auto;
      .innerImage {
        width: 100%;
        height: 0;
        img, video {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      opacity: 0;
      transform: translateY(@vw20-1080);
    }
    .subTitle {
      margin-bottom: @vw20-1080;
    }
    .text {
      margin-top: @vw30-1080;
    }
    .contentWrapper {
      .cols {
        .col {
          vertical-align: middle;
          display: inline-block;
          width: 50%;
          &:first-child {
            padding-right: @vw40-1080;
          }
          &:last-child {
            padding-left: @vw8-1080;
          }
          .formWrapper {
            margin-top: @vw60-1080;
            form {
              margin-left: -@vw8-1080;
              width: calc(100% ~"+" @vw16-1080);
              .field {
                white-space: nowrap;
                .subtitle {
                  white-space: normal;
                }
                margin: 0 @vw8-1080;
                display: inline-block;
                width: calc(100% ~"-" @vw16-1080);
                vertical-align: top;
                &.half {
                  width: calc(50% ~"-" @vw16-1080);
                }
                &.small {
                  width: calc(25% ~"-" @vw16-1080);
                }
              }

              select {
                &.select2-hidden-accessible {
                  width: 100%;
                }
              }

              input, select, textarea {
                width: 100%;
                padding: @vw16-1080;
                border: none;
                .rounded(@vw10-1080);
                font-size: @vw16-1080;
                font-family: 'Figtree', Arial, sans-serif;
                margin-bottom: @vw20-1080;
                box-sizing: border-box;
                background: @lightGrey;
                &:focus {
                  border-color: none;
                  outline: none;
                }

                &::-webkit-input-placeholder {
                  color: #999;
                }

                &:-moz-placeholder {
                  color: #999;
                }

                &::-moz-placeholder {
                  color: #999;
                }

                &:-ms-input-placeholder {
                  color: #999;
                }

                &::placeholder {
                  color: #999;
                }
              }

              textarea {
                resize: none;
              }

              button[type="submit"] {
                background-color: @primaryColor;
                color: @hardWhite;
                padding: @vw16-1080;
                font-size: @vw20-1080;
                line-height: 1;
                border: 1px solid @primaryColor;
                .rounded(@vw10-1080);
                cursor: pointer;
                .transition(.3s);
                &:hover {
                  background-color: @hardWhite;
                  color: @primaryColor;
                }
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
              }

              .select2-container--default .select2-selection--single {
                height: @vw40-1080;
                border: 1px solid #ddd;
                .rounded(@vw10-1080);
              }

              .select2-container .select2-selection--single .select2-selection__rendered {
                padding-left: @vw10-1080;
                line-height: 1;
              }

              .select2-container .select2-selection--single .select2-selection__arrow {
                height: @vw40-1080;
                right: @vw10-1080;
              }

              .error {
                color: #ff0000;
                background-color: #ffe6e6;
                border: 1px solid #ff0000;
                padding: 10px;
                .rounded(@vw10-1080);
                margin-top: 5px;
                font-family: 'Figtree', Arial, sans-serif;
                font-size: @vw12-1080;
                display: block;
              }
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .contactBlock {
    &.inview {
      .subTitle, .text, .imageWrapper {
        opacity: 1;
        transform: translateY(0);
        .transitionMore(all, .45s, .6s, ease-in-out);
      }
    }
    .subTitle, .text, .imageWrapper {
      opacity: 0;
      transform: translateY(@vw20-580);
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .text {
      margin-top: @vw30-580;
    }
    .contentWrapper {
      .cols {
        .col {
          vertical-align: middle;
          display: inline-block;
          width: 100%;
          &:first-child {
            margin-bottom: @vw40-580;
            padding-right:0;
          }
          &:last-child {
            padding-left: 0;
          }
          .imageWrapper {
            width: 100%;
            position: relative;
            overflow: hidden;
            height: auto;
            .innerImage {
              width: 100%;
              height: 0;
              img, video {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                object-position: center;
              }
            }
          }

          .formWrapper {
            margin-top: @vw60-580;
            form {
              margin-left: -@vw8-580;
              width: calc(100% ~"+" @vw16-580);
              .field {
                margin: 0 @vw8-580;
                display: inline-block;
                width: calc(100% ~"-" @vw16-580);
                vertical-align: top;
                &.big {
                  width: calc(66.6666% ~"-" @vw16-580);
                }
                &.half {
                  width: calc(100% ~"-" @vw16-580);
                }
                &.small {
                  width: calc(33.3333% ~"-" @vw16-580);
                }
              }

              select {
                &.select2-hidden-accessible {
                  width: 100%;
                }
              }

              input, select, textarea {
                width: 100%;
                padding: @vw22-580;
                border: none;
                .rounded(@vw10-580);
                font-size: @vw22-580;
                font-family: 'Figtree', Arial, sans-serif;
                margin-bottom: @vw20-580;
                box-sizing: border-box;
                background: @lightGrey;
                &:focus {
                  border-color: none;
                  outline: none;
                }

                &::-webkit-input-placeholder {
                  color: #999;
                }

                &:-moz-placeholder {
                  color: #999;
                }

                &::-moz-placeholder {
                  color: #999;
                }

                &:-ms-input-placeholder {
                  color: #999;
                }

                &::placeholder {
                  color: #999;
                }
              }

              input {
                height: @vw60-580;
                line-height: @vw60-580;
                display: block;
              }

              textarea {
                resize: none;
              }

              button[type="submit"] {
                background-color: @primaryColor;
                color: @hardWhite;
                padding: @vw22-580;
                font-size: @vw20-580;
                line-height: 1;
                border: 1px solid @primaryColor;
                .rounded(@vw10-580);
                cursor: pointer;
                .transition(.3s);
                &:hover {
                  background-color: @hardWhite;
                  color: @primaryColor;
                }
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
              }

              .select2-container--default .select2-selection--single {
                height: @vw40-580;
                border: 1px solid #ddd;
               .rounded(@vw10-580);
              }

              .select2-container .select2-selection--single .select2-selection__rendered {
                padding-left: @vw10-580;
                line-height: 1;
              }

              .select2-container .select2-selection--single .select2-selection__arrow {
                height: @vw40-580;
                right: @vw10-580;
              }

              .error {
                color: #ff0000;
                background-color: #ffe6e6;
                border: 1px solid #ff0000;
                padding: 10px;
                .rounded(@vw10-580);
                margin-top: 5px;
                font-family: 'Figtree', Arial, sans-serif;
                font-size: @vw16-580;
                display: block;
              }
            }
          }
        }
      }
    }
  }
}
