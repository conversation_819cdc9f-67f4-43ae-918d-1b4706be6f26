.contactDetailsBlock {
  .col {
    display: inline-block;
    vertical-align: middle;
    width: 50%;
    &:first-child {
      padding-right: @vw112 + @vw16 + @vw8;
    }
    &:last-child {
      padding-left: @vw8;
    }
    .imageWrapper {
      width: 100%;
      position: relative;
      border-radius: @vw20;
      overflow: hidden;
      height: auto;
      .innerImage {
        width: 100%;
        height: 0;
        .padding-bottom-generator(496, 207);
        img {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
        }
      }
    }
    .innerCols {
      margin-left: -@vw20;
      width: calc(100% ~"+" @vw40);
      .innerCol {
        display: inline-block;
        vertical-align: top;
        margin: 0 @vw20;
        width: calc(33.3333% ~"-" @vw40);
        .normalTitle {
          margin-bottom: @vw20;
        }
        .links {
          margin: @vw30 0;
          .link {
            &:not(:last-child){
              margin-bottom: @vw10;
            }
            display: table;
            text-decoration: none;
            color: @hardBlack;
            cursor: pointer;
            .transition(.3s);
            &:hover {
              opacity: .7;
            }
            i {
              margin-right: @vw5;
            }
            span {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1080px) {
  .contactDetailsBlock {
    .col {
      display: inline-block;
      vertical-align: middle;
      width: 50%;
      &:first-child {
        padding-right: @vw112-1080 + @vw16-1080 + @vw8-1080;
      }
      &:last-child {
        padding-left: @vw8-1080;
      }
      .imageWrapper {
        width: 100%;
        position: relative;
        border-radius: @vw20-1080;
        overflow: hidden;
        height: auto;
        .innerImage {
          width: 100%;
          height: 0;
          .padding-bottom-generator(4, 2);
          img {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
          }
        }
      }
      .innerCols {
        margin-left: -@vw20-1080;
        width: calc(100% ~"+" @vw40-1080);
        .innerCol {
          display: inline-block;
          vertical-align: top;
          margin: 0 @vw20-1080;
          width: calc(50% ~"-" @vw40-1080);
          &:last-child {
            margin-top: @vw30-1080;
          }
          .normalTitle {
            margin-bottom: @vw20-1080;
          }
          .links {
            margin: @vw30-1080 0;
            .link {
              &:not(:last-child){
                margin-bottom: @vw10-1080;
              }
              display: table;
              text-decoration: none;
              color: @hardBlack;
              cursor: pointer;
              .transition(.3s);
              &:hover {
                opacity: .7;
              }
              i {
                margin-right: @vw5-1080;
              }
              span {
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 580px) {
  .contactDetailsBlock {
    .col {
      display: inline-block;
      vertical-align: middle;
      width: 100%;
      &:first-child {
        display: none;
      }
      &:last-child {
        padding-left: @vw8-580;
      }
      .imageWrapper {
        width: 100%;
        position: relative;
        border-radius: @vw20-580;
        overflow: hidden;
        height: auto;
        .innerImage {
          width: 100%;
          height: 0;
          .padding-bottom-generator(496, 207);
          img {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
          }
        }
      }
      .innerCols {
        margin-left: -@vw20-580;
        width: calc(100% ~"+" @vw40-580);
        .innerCol {
          display: inline-block;
          vertical-align: top;
          margin: 0 @vw20-580;
          width: calc(100% ~"-" @vw40-580);
          &:first-child {
            padding-right: @vw100-580;
          }
          &:nth-child(2) {
            margin-top: @vw30-580;
            width: calc(50% ~"-" @vw40-580);
          }
          &:last-child {
            margin-top: @vw30-580;
            width: calc(50% ~"-" @vw40-580);
          }
          .normalTitle {
            margin-bottom: @vw20-580;
          }
          .links {
            margin: @vw30-580 0;
            .link {
              &:not(:last-child){
                margin-bottom: @vw10-580;
              }
              display: table;
              text-decoration: none;
              color: @hardBlack;
              cursor: pointer;
              .transition(.3s);
              &:hover {
                opacity: .7;
              }
              i {
                margin-right: @vw5-580;
              }
              span {
                text-decoration: underline;
              }
            }
          }
        }
      }
    }
  }
}
