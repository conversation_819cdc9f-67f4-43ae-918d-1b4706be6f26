.headerBlock {
  margin-top: 0;
}
.headerBlock .backgroundImage {
  position: relative;
  overflow: hidden;
  height: auto;
  vertical-align: top;
  width: 100%;
}
.headerBlock .backgroundImage .innerImage {
  padding-bottom: 48.14814815%;
  height: 0;
  width: 100%;
}
.headerBlock .backgroundImage .innerImage img,
.headerBlock .backgroundImage .innerImage video {
  position: absolute;
  object-fit: cover;
  object-position: center;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
