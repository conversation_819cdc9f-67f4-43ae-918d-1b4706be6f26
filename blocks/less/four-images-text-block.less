// out: false
.fourImagesTextBlock {
  &.inview {
    .cols {
      .col {
        opacity: 1;
        transform: translateY(0);
        .transitionMore(all, .45s, .6s, ease-in-out);
        .stagger(4, 0.15, .6s);
      }
    }
    .text {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .cols {
    margin-left: -@vw10;
    width: calc(100% ~"+" @vw20);
    .col {
      display: inline-block;
      vertical-align: middle;
      margin: 0 @vw10;
      width: calc(25% ~"-" @vw20);
      opacity: 0;
      transform: translateY(@vw20);
    }
  }
  .text {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .item {
    position: relative;
    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,.2);
    }
    .smallerTitle {
      position: absolute;
      bottom: @vw60;
      width: 100%;
      padding: 0 @vw40;
      left: 50%;
      .transform(translateX(-50%));
      color: @almostWhite;
      right: 0;
      z-index: 1;
      text-align: center;
    }
  }
  .titleWrapper {
    text-align: center;
    margin-bottom: @vw100;
  }
  .imageWrapper {
    height: auto;
    position: relative;
    overflow: hidden;
    width: 100%;
    .innerImage {
      height: 0;
      .paddingRatio(420, 556);
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .text {
    margin-top: @vw30;
  }
}
@media all and (max-width: 1080px) {
  .imageTextBlock {
    .cols {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .col {
        margin: 0 @vw8-1080;
        width: calc(50% ~"-" @vw16-1080);
        &.text {
          padding: 0 @vw40-1080;
        }
        &:not(.text) {
          &:first-child {
            padding-left: @vw50-1080;
            .imageWrapper {
              .innerImage {
                .paddingRatio(624, 528);
              }
            }
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-1080);
    }
    .subTitle {
      margin-bottom: @vw20-1080;
    }
    .text {
      margin-top: @vw30-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .imageTextBlock {
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &.text {
          padding: 0;
        }
        &:not(:last-child){
          margin-bottom: @vw40-580;
        }
        &:not(.text) {
          &:first-child {
            padding-left: 0;
            .imageWrapper {
              .innerImage {
                .paddingRatio(624, 528);
              }
            }
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-580);
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .text {
      margin-top: @vw30-580;
    }
  }
}
