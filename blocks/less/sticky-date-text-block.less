// out: false;
.stickyDateTextBlock {
  &.inview {
    .subTitle, .text {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .cols {
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .col {
      position: relative;
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw8;
      &:first-child {
        width: calc(33.3333% ~"-" @vw16);
      }
      &:last-child {
        width: calc(66.6666% ~"-" @vw16);
      }
    }
  }
  .items {
    .item {
      &:not(:last-child) {
        margin-bottom: @vw100;
      }
      .subTitle {
        display: none;
      }
    }
  }
  .stickyItem {
    padding-top: @vw100 + @vw50;
    width: 100%;
    top: 0;
    .dates {
      height: @vw80;
      position: relative;
      width: 100%;
      overflow: hidden;
      .date {
        position: absolute;
        top: 0;
        visibility: hidden;
        left: 0;
        &.active {
          visibility: visible;
        }
        .letter {
          padding-top: @vw10;
          display: inline-block;
          vertical-align: top;
          width: @vw50;
          text-align: center;
          .transitionMore(all, .3s, 0s, ease-in-out);
          .transitionLoopSplitter(4, .07s);
        }
      }
    }
  }
  .bigTitle {
    &:not(.date) {
      margin: @vw70 0 @vw40 0;
    }
  }
  .subTitle, .text {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .text {
    margin-bottom: @vw100;
    padding-right: @vw40;
  }
  .cols {
    margin-top: @vw40;
  }
  .imageWrapper {
    height: auto;
    border-radius: @vw20;
    overflow: hidden;
    position: relative;
    width: 100%;
    .innerImage {
      height: 0;
      .padding-bottom-generator(880, 587);
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
      }
    }

    &.slideshowWrapper {
      height: 0;
      .padding-bottom-generator(880, 587);
      .innerImage {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
      .slideImage {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
        visibility: hidden;
        &.active {
          opacity: 1;
          visibility: visible;
        }
      }
    }
  }
  section {
    margin: 0;
  }
  .dividerBlock {
    .contentWrapper {
      padding: 0;
      width: 100%;
    }
  }
}
@media all and (max-width: 1080px) {
  .stickyDateTextBlock {
    &.inview {
      .subTitle, .text {
        opacity: 1;
        transform: translateY(0);
        .transitionMore(all, .45s, .6s, ease-in-out);
      }
    }
    .cols {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .col {
        position: relative;
        display: inline-block;
        vertical-align: top;
        margin: 0 @vw8-1080;
        &:first-child {
          width: calc(33.3333% ~"-" @vw16-1080);
        }
        &:last-child {
          width: calc(66.6666% ~"-" @vw16-1080);
        }
      }
    }
    .stickyItem {
      padding-top: @vw100-1080 + @vw50-1080;
      width: 100%;
      top: 0;
      .dates {
        height: @vw80-1080;
        position: relative;
        width: 100%;
        overflow: hidden;
        .date {
          position: absolute;
          top: 0;
          visibility: hidden;
          left: 0;
          &.active {
            visibility: visible;
          }
          .letter {
            padding-top: @vw10-1080;
            display: inline-block;
            vertical-align: top;
            width: @vw50-1080;
            text-align: center;
            .transitionMore(all, .3s, 0s, ease-in-out);
            .transitionLoopSplitter(4, .07s);
          }
        }
      }
    }
    .bigTitle {
      &:not(.date) {
        margin: @vw70-1080 0 @vw40-1080 0;
      }
    }
    .subTitle, .text {
      opacity: 0;
      transform: translateY(@vw20-1080);
    }
    .subTitle {
      margin-bottom: @vw20-1080;
    }
    .text {
      padding-right: @vw40-1080;
      margin-bottom: @vw100-1080;
    }
    .cols {
      margin-top: @vw40-1080;
    }
    .items {
      .item {
        &:not(:last-child) {
          margin-bottom: @vw100-1080;
        }
      }
    }
    .imageWrapper {
      height: auto;
      border-radius: @vw20-1080;
      overflow: hidden;
      position: relative;
      width: 100%;
      .innerImage {
        height: 0;
        .padding-bottom-generator(880, 587);
        img {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          object-fit: cover;
          object-position: center;
        }
      }

      &.slideshowWrapper {
        .slideImage {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
          visibility: hidden;
          transition: opacity 0.5s ease-in-out;

          &.active {
            opacity: 1;
            visibility: visible;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .stickyDateTextBlock {
    &.inview {
      .subTitle, .text {
        opacity: 1;
        transform: translateY(0);
        .transitionMore(all, .45s, .6s, ease-in-out);
      }
    }
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        position: relative;
        display: inline-block;
        vertical-align: top;
        margin: 0 @vw8-580;
        &:first-child {
          width: calc(100% ~"-" @vw16-580);
        }
        &:last-child {
          width: calc(100% ~"-" @vw16-580);
        }
      }
    }
    .stickyItem {
      display: none;
    }
    .bigTitle {
      &:not(.date) {
        margin: 0 0 @vw40-580 0;
      }
    }
    .subTitle, .text {
      opacity: 0;
      transform: translateY(@vw20-580);
    }
    .subTitle {
      margin-top: @vw40-580;
      margin-bottom: @vw20-580;
    }
    .text {
      padding-right: @vw40-580;
      margin-bottom: @vw100-580;
    }
    .cols {
      margin-top: @vw40-580;
    }
    .items {
      .item {
        &:not(:last-child) {
          margin-bottom: @vw100-580;
        }
        .subTitle {
          display: block;
          margin-bottom: @vw22-580;
        }
      }
    }
    .imageWrapper {
      height: auto;
      border-radius: @vw20-580;
      overflow: hidden;
      position: relative;
      width: 100%;
      .innerImage {
        height: 0;
        .padding-bottom-generator(880, 587);
        img {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          object-fit: cover;
          object-position: center;
        }
      }

      &.slideshowWrapper {
        .slideImage {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          opacity: 0;
          visibility: hidden;
          transition: opacity 0.5s ease-in-out;

          &.active {
            opacity: 1;
            visibility: visible;
          }
        }
      }
    }
  }
}
