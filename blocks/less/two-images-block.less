// out: false
.twoImagesBlock {
  &.inview {
    .subTitle, .text, .imageWrapper {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .cols {
    margin-left: -@vw10;
    width: calc(100% ~"+" @vw20);
    .col {
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw10;
      width: calc(50% ~"-" @vw20);
      &:first-child {
        .imageWrapper {
          .innerImage {
            .paddingRatio(635, 597);
          }
        }
      }
      &:last-child {
        margin-top: @vw200;
        text-align: right;
        .imageWrapper {
          margin-left: auto;
          .innerImage {
            .paddingRatio(640, 755);
          }
        }
      }
      &.text {
          padding: 0 @vw112 + @vw20;
      }
    }
  }
  .subTitle, .text, .imageWrapper {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .imageWrapper {
    width: (@vw200 * 3) + (@vw16 * 2);
    height: auto;
    position: relative;
    overflow: hidden;
    .innerImage {
      height: 0;
      .innerTransform {
        position: absolute;
        top: -10%;
        left: 0;
        width: 100%;
        height: 120%;
      }
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .text {
    margin-top: @vw30;
  }
  .buttonWrapper {
    margin-top: @vw40;
    .button, .extraText {
      display: inline-block;
      vertical-align: middle;
      margin-top: 0;
      &:not(:last-child) {
        margin-right: @vw16;
      }
    }
    .extraText {
      margin-top: @vw22;
    }
  }
  .extraText {
    text-transform: uppercase;
    p {
      font-size: @vw14;
      letter-spacing: 0.08rem;
    }
  }
}
@media all and (max-width: 1080px) {
  .imageTextBlock {
    .cols {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .col {
        margin: 0 @vw8-1080;
        width: calc(50% ~"-" @vw16-1080);
        &.text {
          padding: 0 @vw40-1080;
        }
        &:not(.text) {
          &:first-child {
            padding-left: @vw50-1080;
            .imageWrapper {
              .innerImage {
                .paddingRatio(624, 528);
              }
            }
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-1080);
    }
    .subTitle {
      margin-bottom: @vw20-1080;
    }
    .text {
      margin-top: @vw30-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .imageTextBlock {
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &.text {
          padding: 0;
        }
        &:not(:last-child){
          margin-bottom: @vw40-580;
        }
        &:not(.text) {
          &:first-child {
            padding-left: 0;
            .imageWrapper {
              .innerImage {
                .paddingRatio(624, 528);
              }
            }
          }
        }
      }
    }
    .subTitle, .text, .imageWrapper {
      transform: translateY(@vw20-580);
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .text {
      margin-top: @vw30-580;
    }
  }
}
