// out: false
.giftCardBlock {
  &.inview {
    .subTitle, .text, .imageWrapper {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .card {
    background: @primaryColorDark;
    padding: @vw40;
    position: relative;
    &:after {
      content: '';
      background: transparent;
      position: absolute;
      top: 50%;
      border: 1px solid @almostWhite;
      left: 50%;
      .transform(translate(-50%, -50%));
      height: calc(100% ~"-" @vw40);
      width: calc(100% ~"-" @vw40);
      pointer-events: none;
    }
  }
  .cols {
    margin-left: -@vw10;
    width: calc(100% ~"+" @vw20);
    .col {
      display: inline-block;
      vertical-align: middle;
      margin: 0 @vw10;
      width: calc(50% ~"-" @vw20);
      &.text {
        padding: 0 @vw60;
        text-align: center;
      }
    }
  }
  .subTitle, .text, .imageWrapper {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .imageWrapper {
    height: auto;
    position: relative;
    overflow: hidden;
    width: 100%;
    .innerImage {
      height: 0;
      .paddingRatio(1, 1);
      img {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .text {
    margin-top: @vw30;
  }
  .textLink {
    margin-top: @vw45;
  }
}
@media all and (max-width: 1080px) {
  
}

@media all and (max-width: 580px) {
}
