.openingTimesBlock {
  &.inview {
    .subTitle, .text {
      opacity: 1;
      transform: translateY(0);
      .transitionMore(all, .45s, .6s, ease-in-out);
    }
  }
  .bigTitle {
    max-width: 100%;
    width: calc(50% ~"-" @vw8);
  }
  .cols {
    margin-left: -@vw8;
    width: calc(100% ~"+" @vw16);
    .col {
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw8;
      width: calc(50% ~"-" @vw16);
    }
  }
  .subTitle, .text {
    opacity: 0;
    transform: translateY(@vw20);
  }
  .times {
    display: inline-block;
    width: 50%;
    vertical-align: top;
    .items {
      display: block;
      .item {
        display: block;
        .day, .time {
          width: 50%;
          display: inline-block;
          vertical-align: top;
        }
      }
    }
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .text {
    padding-right: @vw40;
  }
  .cols {
    margin-top: @vw40;
  }
}

@media screen and (max-width: 1080px) {
  .openingTimesBlock {
    &.inview {
      .subTitle, .text {
        opacity: 1;
        transform: translateY(0);
        .transitionMore(all, .45s, .6s, ease-in-out);
      }
    }
    .bigTitle {
      max-width: 100%;
      width: calc(50% ~"-" @vw8-1080);
    }
    .cols {
      margin-left: -@vw8-1080;
      width: calc(100% ~"+" @vw16-1080);
      .col {
        display: inline-block;
        vertical-align: top;
        margin: 0 @vw8-1080;
        width: calc(100% ~"-" @vw16-1080);
        &:not(:last-child) {
          margin-bottom: @vw80-1080;
        }
      }
    }
    .subTitle, .text {
      opacity: 0;
      transform: translateY(@vw20-1080);
    }
    .times {
      display: inline-block;
      width: 50%;
      vertical-align: top;
      .items {
        display: block;
        .item {
          display: block;
          .day, .time {
            width: 50%;
            display: inline-block;
            vertical-align: top;
          }
        }
      }
    }
    .subTitle {
      margin-bottom: @vw20-1080;
    }
    .text {
      padding-right: @vw40-1080;
    }
    .cols {
      margin-top: @vw40-1080;
    }
  }
}

@media screen and (max-width: 580px) {
  .openingTimesBlock {
    &.inview {
      .subTitle, .text {
        opacity: 1;
        transform: translateY(0);
        .transitionMore(all, .45s, .6s, ease-in-out);
      }
    }
    .bigTitle {
      max-width: 100%;
      width: calc(100% ~"-" @vw8-580);
    }
    .cols {
      margin-left: -@vw8-580;
      width: calc(100% ~"+" @vw16-580);
      .col {
        display: inline-block;
        vertical-align: top;
        margin: 0 @vw8-580;
        width: calc(100% ~"-" @vw16-580);
        &:not(:last-child) {
          margin-bottom: @vw40-580;
        }
      }
    }
    .subTitle, .text {
      opacity: 0;
      transform: translateY(@vw20-580);
    }
    .times {
      display: inline-block;
      width: 100%;
      vertical-align: top;
      &:not(:last-child) {
        margin-bottom: @vw40-580;
      }
      .items {
        display: block;
        .item {
          display: block;
          .day, .time {
            width: 50%;
            display: inline-block;
            vertical-align: top;
          }
        }
      }
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .text {
      padding-right: @vw40-580;
    }
    .cols {
      margin-top: @vw40-580;
    }
  }
}
