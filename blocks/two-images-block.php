<?php
/**
 * Two Images Block Template
 *
 * @param array $block The block settings and attributes.
 * @param string $content The block inner HTML (empty).
 * @param bool $is_preview True during AJAX preview.
 * @param int|string $post_id The post ID this block is saved to.
 */

// Create id attribute allowing for custom "anchor" value.
$id = 'two-images-' . $block['id'];
if (!empty($block['anchor'])) {
    $id = $block['anchor'];
}

// Create class attribute allowing for custom "className" and "align" values.
$className = 'twoImagesBlock';
if (!empty($block['className'])) {
    $className .= ' ' . $block['className'];
}
if (!empty($block['align'])) {
    $className .= ' align' . $block['align'];
}

// Load values and assign defaults.
$image_1 = get_field('image_1');
$image_2 = get_field('image_2');
?>

<section id="<?php echo esc_attr($id); ?>" class="twoImagesBlock" data-init>
  <div class="contentWrapper">
    <div class="cols">
      <?php if ($image_1): ?>
      <div class="col image" data-parallax data-parallax-speed="4">
        <div class="imageWrapper">
          <div class="innerImage">
            <div class="innerTransform" data-parallax data-parallax-speed="1">
              <img class="lazy" data-src="<?php echo esc_url($image_1['sizes']['medium_large']); ?>" alt="<?php echo esc_attr($image_1['alt']); ?>"/>
            </div>
          </div>
        </div>
      </div>
      <?php endif; ?>

      <?php if ($image_2): ?>
      <div class="col image">
        <div class="imageWrapper">
          <div class="innerImage">
            <div class="innerTransform" data-parallax data-parallax-speed="1">
              <img class="lazy" data-src="<?php echo esc_url($image_2['sizes']['large']); ?>" alt="<?php echo esc_attr($image_2['alt']); ?>"/>
            </div>
          </div>
        </div>
      </div>
      <?php endif; ?>
    </div>
  </div>
</section>