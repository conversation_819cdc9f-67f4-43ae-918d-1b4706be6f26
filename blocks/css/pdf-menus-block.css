/**
 * PDF Menus Block Styles
 */

.pdfMenusBlock {
    padding: 60px 0;
    background: #fff;
}

.pdfMenusBlock .contentWrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Block Header */
.pdfMenusBlock .blockHeader {
    text-align: center;
    margin-bottom: 50px;
}

.pdfMenusBlock .blockTitle {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    line-height: 1.2;
}

.pdfMenusBlock .blockTitle .primary {
    color: var(--primary-color, #d4af37);
}

.pdfMenusBlock .blockSubtitle {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* Menus Container */
.pdfMenusBlock .menusContainer {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Filter Section */
.pdfMenusBlock .menusFilter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

.pdfMenusBlock .filterButtons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.pdfMenusBlock .filterBtn {
    padding: 10px 20px;
    border: 2px solid #e0e0e0;
    background: #fff;
    color: #333;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.pdfMenusBlock .filterBtn:hover {
    border-color: var(--primary-color, #d4af37);
    color: var(--primary-color, #d4af37);
}

.pdfMenusBlock .filterBtn.active {
    background: var(--primary-color, #d4af37);
    border-color: var(--primary-color, #d4af37);
    color: #fff;
}

.pdfMenusBlock .downloadSection {
    display: flex;
    align-items: center;
}

.pdfMenusBlock .downloadBtn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: var(--primary-color, #d4af37);
    color: #fff;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.pdfMenusBlock .downloadBtn:hover:not(:disabled) {
    background: var(--primary-color-dark, #b8941f);
    transform: translateY(-2px);
}

.pdfMenusBlock .downloadBtn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.pdfMenusBlock .downloadIcon {
    font-size: 1.1rem;
}

/* Menus Display */
.pdfMenusBlock .menusDisplay {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    min-height: 600px;
}

/* Menu List */
.pdfMenusBlock .menusList {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    overflow-y: auto;
    max-height: 600px;
}

.pdfMenusBlock .categoryMenus {
    margin-bottom: 20px;
}

.pdfMenusBlock .categoryMenus:last-child {
    margin-bottom: 0;
}

.pdfMenusBlock .menuItem {
    background: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pdfMenusBlock .menuItem:hover {
    border-color: var(--primary-color, #d4af37);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.pdfMenusBlock .menuItem.active {
    border-color: var(--primary-color, #d4af37);
    background: #fff9e6;
}

.pdfMenusBlock .menuItem:last-child {
    margin-bottom: 0;
}

.pdfMenusBlock .menuInfo {
    margin-bottom: 15px;
}

.pdfMenusBlock .menuName {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.pdfMenusBlock .menuDescription {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 10px;
    line-height: 1.4;
}

.pdfMenusBlock .menuMeta {
    display: flex;
    gap: 15px;
    font-size: 0.8rem;
    color: #888;
}

.pdfMenusBlock .menuCategory {
    background: var(--primary-color, #d4af37);
    color: #fff;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.pdfMenusBlock .menuActions {
    display: flex;
    justify-content: flex-end;
}

.pdfMenusBlock .viewBtn {
    padding: 8px 16px;
    background: transparent;
    color: var(--primary-color, #d4af37);
    border: 1px solid var(--primary-color, #d4af37);
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.pdfMenusBlock .viewBtn:hover {
    background: var(--primary-color, #d4af37);
    color: #fff;
}

/* PDF Viewer */
.pdfMenusBlock .pdfViewer {
    background: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
}

.pdfMenusBlock .pdfContainer {
    height: 600px;
    position: relative;
}

.pdfMenusBlock .pdfPlaceholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f8f9fa;
}

.pdfMenusBlock .placeholderContent {
    text-align: center;
    color: #666;
}

.pdfMenusBlock .placeholderIcon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.pdfMenusBlock .placeholderContent h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.pdfMenusBlock .placeholderContent p {
    font-size: 1rem;
    color: #666;
}

.pdfMenusBlock .pdfFrame {
    width: 100%;
    height: 100%;
    border: none;
    transition: opacity 0.3s ease;
}

/* No Menus State */
.pdfMenusBlock .noMenus {
    text-align: center;
    padding: 60px 20px;
    background: #f8f9fa;
    border-radius: 12px;
}

.pdfMenusBlock .noMenus p {
    font-size: 1.1rem;
    color: #666;
}

.pdfMenusBlock .noMenus a {
    color: var(--primary-color, #d4af37);
    text-decoration: none;
    font-weight: 600;
}

.pdfMenusBlock .noMenus a:hover {
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .pdfMenusBlock {
        padding: 40px 0;
    }
    
    .pdfMenusBlock .blockTitle {
        font-size: 2rem;
    }
    
    .pdfMenusBlock .menusFilter {
        flex-direction: column;
        align-items: stretch;
    }
    
    .pdfMenusBlock .filterButtons {
        justify-content: center;
    }
    
    .pdfMenusBlock .menusDisplay {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .pdfMenusBlock .pdfContainer {
        height: 400px;
    }
    
    .pdfMenusBlock .menusList {
        max-height: 300px;
    }
}

@media (max-width: 480px) {
    .pdfMenusBlock .filterBtn {
        padding: 8px 16px;
        font-size: 0.85rem;
    }
    
    .pdfMenusBlock .downloadBtn {
        padding: 10px 20px;
        font-size: 0.85rem;
    }
    
    .pdfMenusBlock .menuItem {
        padding: 15px;
    }
    
    .pdfMenusBlock .menuName {
        font-size: 1.1rem;
    }
}
