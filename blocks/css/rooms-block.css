/* Rooms Block Styles */
.rooms-block {
    margin: 2rem 0;
    padding: 0;
}

.rooms-container {
    display: grid;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

/* Grid Layout Columns */
.rooms-grid.rooms-columns-1 { 
    grid-template-columns: 1fr; 
}

.rooms-grid.rooms-columns-2 { 
    grid-template-columns: repeat(2, 1fr); 
}

.rooms-grid.rooms-columns-3 { 
    grid-template-columns: repeat(3, 1fr); 
}

.rooms-grid.rooms-columns-4 { 
    grid-template-columns: repeat(4, 1fr); 
}

/* List Layout */
.rooms-list {
    grid-template-columns: 1fr;
}

.rooms-list .room-item {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    align-items: start;
}

/* Room Item Styles */
.room-item {
    background: #ffffff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.room-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--room-color, #3498db);
}

/* Room Image */
.room-image {
    position: relative;
    height: 280px;
    overflow: hidden;
    background: #f8f9fa;
}

.room-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.room-item:hover .room-image img {
    transform: scale(1.08);
}

.room-gallery-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
    backdrop-filter: blur(10px);
}

.room-gallery-indicator:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* Room Content */
.room-content {
    padding: 2rem;
}

.room-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.room-title {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--room-color, #2c3e50);
    line-height: 1.3;
    flex: 1;
}

.room-price {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--room-color, #2c3e50);
    text-align: right;
    white-space: nowrap;
}

.price-period {
    font-size: 1rem;
    font-weight: 400;
    color: #666;
    display: block;
    margin-top: 2px;
}

/* Room Description */
.room-description {
    margin-bottom: 1.5rem;
    color: #555;
    line-height: 1.7;
    font-size: 1rem;
}

.room-description p {
    margin-bottom: 1rem;
}

.room-description p:last-child {
    margin-bottom: 0;
}

/* Room Details */
.room-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 12px;
}

.room-detail {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.95rem;
    color: #555;
    font-weight: 500;
}

.detail-icon {
    font-size: 1.25rem;
    opacity: 0.8;
}

/* Room Amenities */
.room-amenities {
    margin-bottom: 2rem;
}

.amenities-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.amenities-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.amenity-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    color: #555;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.amenity-item:hover {
    background: var(--room-color, #3498db);
    color: white;
    border-color: var(--room-color, #3498db);
}

.amenity-icon {
    width: 18px;
    height: 18px;
    object-fit: contain;
    opacity: 0.8;
}

/* Room Actions */
.room-actions {
    text-align: center;
    margin-top: 2rem;
}

.room-link {
    display: inline-block;
    padding: 1rem 2.5rem;
    background: var(--room-color, #3498db);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.room-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.room-link:hover::before {
    left: 100%;
}

.room-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    filter: brightness(1.1);
}

/* No Rooms Message */
.no-rooms-message {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .rooms-grid.rooms-columns-4 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .rooms-grid.rooms-columns-3 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .rooms-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem;
    }
    
    .rooms-list .room-item {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .room-content {
        padding: 1.5rem;
    }
    
    .room-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .room-price {
        text-align: left;
    }
    
    .room-details {
        flex-direction: column;
        gap: 1rem;
    }
    
    .room-title {
        font-size: 1.5rem;
    }
    
    .room-image {
        height: 220px;
    }
}

@media (max-width: 480px) {
    .rooms-block {
        margin: 1rem 0;
    }
    
    .rooms-container {
        gap: 1rem;
    }
    
    .room-content {
        padding: 1rem;
    }
    
    .room-title {
        font-size: 1.25rem;
    }
    
    .room-price {
        font-size: 1.25rem;
    }
    
    .room-link {
        padding: 0.875rem 2rem;
        font-size: 0.95rem;
    }
}
