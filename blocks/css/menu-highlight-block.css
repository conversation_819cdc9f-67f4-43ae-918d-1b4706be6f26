@lighter Grey;.menuHightlightBlock .imagesWrapper{height:100vh;width:100%;min-height:34.722vw}.menuHightlightBlock .imagesWrapper .backgroundImage{border-radius:1.157vw;overflow:hidden;width:100%;height:100%;position:absolute;top:0;left:0}.menuHightlightBlock .imagesWrapper .backgroundImage img{width:100%;height:100%;position:absolute;top:0;left:0;object-fit:cover;object-position:center}.menuHightlightBlock .imagesWrapper .menu{cursor:zoom-in;display:block;position:absolute;width:auto;transform:translate(-50%, -50%);left:50%;top:50%;height:calc(100% - 6.944vw)}.menuHightlightBlock .imagesWrapper .menu *{cursor:zoom-in}.menuHightlightBlock .imagesWrapper .menu:hover:before{opacity:.8}.menuHightlightBlock .imagesWrapper .menu:hover:after{opacity:1;transform:translate(-50%, -50%) rotate(0deg)}