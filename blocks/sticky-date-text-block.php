<?php
$pattern = '/(.*)\*(.*)\*(.*)/';
$size = 'large';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="stickyDateTextBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper smaller">
    <div class="cols">
      <div class="col">
        <div class="stickyItem">
          <div class="subTitle primary"><?php the_field("subtitle") ?></div>
          <div class="dates">
            <?php
            if( have_rows('items') ): $row_index = 0;
              while( have_rows('items') ): the_row();
              $row_index++;
              $date = get_sub_field('date');
                if( $date && strtotime($date) ): ?>
                  <div class="bigTitle date <?php if($row_index == 1): ?> active <?php endif; ?>"><?php echo esc_html( date_i18n( 'Y', strtotime($date) ) ); ?></div>
              <?php
              endif;
            endwhile;
            endif;
            ?>
          </div>
        </div>
      </div>
      <div class="col">
        <?php if( have_rows('items') ): $total_rows = count( get_field('items') ); $row_index = 0; ?>
            <div class="items">
                <?php while( have_rows('items') ): the_row(); ?>
                    <?php
                    $row_index++;
                    $title = str_replace(['<p>', '</p>'], '', get_sub_field("title"));
                    $title = preg_replace($pattern, $replacement, $title);
                    $video = get_sub_field('video');
                    $image = get_sub_field('image');
                    $images = get_sub_field('image');
                    $slideshow_interval = get_sub_field('slideshow_interval') ? get_sub_field('slideshow_interval') : 3;
                    $text = get_sub_field('text');
                    $date = get_sub_field('date');
                    ?>

                    <div class="item">
                        <?php if( $images && count($images) > 1 ): ?>
                          <div class="imageWrapper slideshowWrapper" data-interval="<?php echo esc_attr($slideshow_interval); ?>">
                            <?php foreach($images as $index => $img): ?>
                              <div class="slideImage <?php echo ($index === 0) ? 'active' : ''; ?>">
                                <div class="innerImage">
                                  <img class="lazy"
                                    alt="<?php echo isset($img['alt']) ? esc_attr($img['alt']) : ''; ?>"
                                    data-src="<?php echo isset($img['sizes']) && isset($img['sizes'][$size]) ? esc_url($img['sizes'][$size]) : (isset($img['url']) ? esc_url($img['url']) : ''); ?>"/>
                                </div>
                              </div>
                            <?php endforeach; ?>
                          </div>
                        <?php elseif( $images && count($images) === 1 ): ?>
                          <div class="imageWrapper">
                            <div class="innerImage">
                              <img class="lazy"
                                alt="<?php echo isset($images[0]['alt']) ? esc_attr($images[0]['alt']) : ''; ?>"
                                data-src="<?php echo isset($images[0]['sizes']) && isset($images[0]['sizes'][$size]) ? esc_url($images[0]['sizes'][$size]) : (isset($images[0]['url']) ? esc_url($images[0]['url']) : ''); ?>"/>
                            </div>
                          </div>
                        <?php elseif( $image ): ?>
                          <div class="imageWrapper">
                            <div class="innerImage">
                              <img class="lazy"
                                alt="<?php echo isset($image['alt']) ? esc_attr($image['alt']) : ''; ?>"
                                data-src="<?php echo isset($image['sizes']) && isset($image['sizes'][$size]) ? esc_url($image['sizes'][$size]) : (isset($image['url']) ? esc_url($image['url']) : ''); ?>"/>
                            </div>
                          </div>
                        <?php endif; ?>

                        <?php if( $video ): ?>
                            <div class="video">
                                <?php echo wp_oembed_get( $video ); // Geeft een video-embed terug ?>
                            </div>
                        <?php endif;
                         if( $date && strtotime($date) ): ?>
                          <div class="subTitle primary"><?php echo esc_html( date_i18n( 'Y', strtotime($date) ) ); ?></div>
                        <?php endif; ?>
                        <h2 class="bigTitle splitThis" data-init data-split><?php echo $title; ?></h2>
                        <?php if( $text ): ?>
                            <div class="text">
                                <?php echo $text; ?>
                            </div>
                        <?php endif; ?>
                        <?php if( $row_index < $total_rows ): include("divider-block.php"); endif; ?>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <p><?php _e('Geen items gevonden.', 'text-domain'); ?></p>
        <?php endif; ?>
      </div>
    </div>
  </div>
</section>
