<?php
$partners = get_posts(array(
  'post_type' => 'partner',
  'posts_per_page' => -1,
  'orderby' => 'rand',
  'order' => 'random'
));
?>
<section class="partnersMarqueeBlock dark fullBackground" data-init>
  <div class="contentWrapper">
    <?php if (get_field("title")) : ?>
        <h2 class="textTitle"><?php the_field("title"); ?></h2>
    <?php endif; ?>
    <div class="marqueeWrapper">
        <?php if (get_field("background_image")) : ?>
            <div class="backgroundImage">
                <img class="lazy" data-src="<?php echo esc_url(get_field('background_image')['sizes']['medium_large']); ?>" alt="<?php echo esc_attr(get_field('background_image')['alt']); ?>">
            </div>
        <?php endif; ?>
        <? if (!get_field("hide_sponsors")) : ?>
        <div class="marquee" data-marquee data-marquee-direction="right" data-marquee-speed="30" data-marquee-scroll-speed="5" data-marquee-swipe="true">
            <div class="marqueeScroll">    
                <div class="itemsContainer">
                    <?php 
                    if ($partners) : 
                        foreach ($partners as $partner) : 
                            $partner_id = $partner->ID;
                    ?>
                        <div class="item partner">
                            <?php if (get_field('partner_link', $partner_id)) : ?>
                                <a title="Visit <?php echo get_the_title($partner_id); ?>" href="<?php echo esc_url(get_field('partner_link', $partner_id)); ?>" target="_blank" class="partnerLink">
                            <?php endif; ?>
                            
                            <?php if (get_field('partner_logo', $partner_id)) : ?>
                                <img src="<?php echo esc_url(get_field('partner_logo', $partner_id)['sizes']['medium']); ?>" alt="<?php echo esc_attr(get_the_title($partner_id)); ?>">
                            <?php else : ?>
                                <?php echo get_the_post_thumbnail($partner_id, 'medium'); ?>
                            <?php endif; ?>
                            
                            <?php if (get_field('partner_link', $partner_id)) : ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php 
                        endforeach;
                    endif; 
                    ?>
                </div>
            </div>
        </div>
        <? endif; ?>
    </div>
  </div>
</section>

