<?php
$size = 'large'; // (thumbnail, medium, large, full or custom size)
$logo = get_field("logo");
$video = get_field("video");
$image = get_field("header_image");
?>
<section class="homeHeaderBlock" data-init>
    <div class="stickyWrapper">
        <div class="stickyCols">
          <div class="row">
            <div class="logo">
              <svg class="toLeft" width="552.294" height="328.008" viewBox="0 0 552.294 328.008">
                <g data-name="Group 212" transform="translate(-403.944 -301.798)">
                  <path data-name="Path 105" d="M0,2.4v54H53.771V330.407h58.7V56.4h53.772V2.4Z" transform="translate(403.944 299.398)" fill="#191816"/>
                  <path data-name="Path 106" d="M246.4,136.829h-58.03V2.4H129.449V330.407h58.924V190.6H246.4V330.407h58.924V2.4H246.4Z" transform="translate(467.855 299.398)" fill="#191816"/>
                  <path data-name="Path 107" d="M274.2,330.408H416.917v-54H333.124V193.589h72.144v-56.76H333.124V56.4h83.793V2.4H274.2Z" transform="translate(539.321 299.398)" fill="#191816"/>
                </g>
              </svg>
              <svg class="toRight" width="443.387" height="335.177" viewBox="0 0 443.387 335.177">
                <g data-name="Group 211" transform="translate(-1072.671 -298.214)">
                  <path data-name="Path 9" d="M447.692,330.408h58.926V193.589h72.144v-56.76H506.618V56.4h83.795V2.4H447.692Z" transform="translate(624.979 299.398)" fill="#191816"/>
                  <rect data-name="Rectangle 46" width="58.926" height="328.007" transform="translate(1249.972 301.799)" fill="#191816"/>
                  <path data-name="Path 10" d="M707.794,194.774h37.341V241.75c0,19.285-3.864,27.1-9.666,32.9-4.393,4.392-9.878,6.528-16.771,6.528-8.057,0-15.378-2.075-19.834-6.529-12.184-12.183-12.8-48.621-12.8-107.062s.618-94.881,12.8-107.064C703.321,56.071,709.148,54,717.2,54c12.434,0,25.3,4.542,26.44,38.239l.146,4.329h58.691l-.14-4.616C800.623,35.232,768,0,717.2,0c-24.968,0-45.415,7.912-60.748,23.493-26.791,26.791-29.319,60.8-29.319,144.1s2.524,117.293,29.307,144.083c15.357,15.6,35.8,23.505,60.76,23.505,24.283,0,45.127-7.882,60.3-22.821,16.48-16.48,24.838-40.011,24.838-66.949V138.014H707.794Z" transform="translate(713.575 298.214)" fill="#191816"/>
                </g>
              </svg>
          </div>
        </div>
        <div class="imageRow">
          <div class="imageWrapper">
            <img class="lazy" data-src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>"/>
          </div>
        </div>
        <div class="row">
          <div class="innerContent">
            <h1 class="title" data-lines data-words><?php the_field("main_title") ?></h1>
            <div class="arrowDown">
              <div class="innerArrow">
                <svg width="35" height="70" viewBox="0 0 35 70">
                  <path data-name="Path 2" d="M0,194.825c9.665,0,17.5,8.407,17.5,18.778" transform="translate(0 -143.603)" fill="none" stroke="#191816" stroke-miterlimit="10" stroke-width="2"/>
                  <path data-name="Path 3" d="M88.924,194.825c-9.665,0-17.5,8.407-17.5,18.778" transform="translate(-53.924 -143.603)" fill="none" stroke="#191816" stroke-miterlimit="10" stroke-width="2"/>
                  <line data-name="Line 4" y2="70" transform="translate(17.5 0)" fill="none" stroke="#191816" stroke-miterlimit="10" stroke-width="2"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>
  <div class="detailsWrapper" data-init>
    <div class="contentWrapper">
      <div class="cols">
        <div class="col" data-parallax data-parallax-speed="1">
            <h2 class="title" data-lines data-words><?php the_field("location_title") ?></h2>
            <div class="text">
              <p>
               <?php echo esc_html(get_theme_mod('customTheme-main-callout-address')); ?>
               </p>
            </div>
              <?php
                $link = get_field("location_link");
                if( $link ) {
                    $link_url = $link['url'];
                    $link_title = $link['title'];
                    $link_target = $link['target'] ? $link['target'] : '_self';
                }
                ?>
              <?php include("parts/textlink.php"); ?>
        </div>
        <div class="divider"></div>
        <div class="col" data-parallax data-parallax-speed="2">
            <h2 class="title" data-lines data-words><?php the_field("hours_title") ?></h2>
            <div class="text">
              <p>
              <?php the_field("hours_text") ?>
              </p>
            </div>
            <?php
              $link = get_field("hours_link");
              if( $link ) {
                  $link_url = $link['url'];
                  $link_title = $link['title'];
                  $link_target = $link['target'] ? $link['target'] : '_self';
              }
              ?>
            <?php include("parts/textlink.php"); ?>
        </div>
        <div class="divider"></div>
        <div class="col" data-parallax data-parallax-speed="3">
            <h2 class="title" data-lines data-words><?php the_field("contact_title") ?></h2>
            <div class="text">
              <p>
              <?php the_field("contact_text") ?>
              </p>
            </div>
            <?php
              $link = get_field("contact_link");
              if( $link ) {
                  $link_url = $link['url'];
                  $link_title = $link['title'];
                  $link_target = $link['target'] ? $link['target'] : '_self';
              }
              ?>
            <?php include("parts/textlink.php"); ?>
        </div>
      </div>
    </div>
  </div>
</section>
