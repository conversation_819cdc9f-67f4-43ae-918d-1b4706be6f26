<?php
/**
 * PDF Menus Block Template
 * 
 * Displays restaurant menus with filtering and PDF viewing functionality
 */

// Get block settings
$title = get_field('title');
$subtitle = get_field('subtitle');
$show_all_filter = get_field('show_all_filter');
$default_category = get_field('default_category');

// Process title for highlighting
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
$title = preg_replace($pattern, $replacement, $title);

// Get menus from options page
$menus = get_field('menus_list', 'option');

// Organize menus by category
$menus_by_category = array();
$all_categories = array();

if ($menus) {
    foreach ($menus as $menu) {
        $category = $menu['menu_category'];
        if (!isset($menus_by_category[$category])) {
            $menus_by_category[$category] = array();
        }
        $menus_by_category[$category][] = $menu;
        
        if (!in_array($category, $all_categories)) {
            $all_categories[] = $category;
        }
    }
}

// Category labels
$category_labels = array(
    'food' => 'Food',
    'drinks' => 'Drinks', 
    'wine' => 'Wine',
    'dessert' => 'Dessert',
    'special' => 'Special'
);

// Generate unique ID for this block instance
$block_id = 'pdf-menus-' . uniqid();
?>

<section class="pdfMenusBlock" data-init id="<?php echo $block_id; ?>">
    <div class="contentWrapper">
        <?php if ($menus): ?>
        <div class="menusContainer">
            
            <!-- Filter Navigation -->
            <div class="menusFilter">
                <div class="filterButtons">
                    <div class="menusList">
                        <?php foreach ($menus_by_category as $category => $category_menus): ?>
                            <div class="categoryMenus" data-category="<?php echo esc_attr($category); ?>" 
                                style="<?php echo ($default_category !== 'all' && $default_category !== $category) ? 'display: none;' : ''; ?>">
                                
                                <?php foreach ($category_menus as $index => $menu): ?>
                                    <?php 
                                    $menu_pdf = $menu['menu_pdf'];
                                    $is_first = ($index === 0 && ($default_category === $category || $default_category === 'all'));
                                    ?>
                                    <div class="menuItem <?php echo $is_first ? 'active' : ''; ?>" 
                                        data-pdf-url="<?php echo esc_url($menu_pdf['url']); ?>"
                                        data-menu-name="<?php echo esc_attr($menu['menu_name']); ?>">
                                        
                                        <div class="menuInfo">
                                            <h3 class="subTitle"><?php echo esc_html($menu['menu_name']); ?></h3>
                                            <button class="viewBtn" data-action="view"></button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Download Button -->
                <div class="downloadSection">
                    <button class="downloadBtn" id="downloadBtn-<?php echo $block_id; ?>" disabled>
                        <span class="downloadIcon"><i class="icon-print"></i></span>
                        <span class="subTitle">Download Menu</span>
                    </button>
                </div>
            </div>

            <!-- Menus Display -->
            <div class="menusDisplay">
                <!-- PDF Viewer -->
                <div class="pdfViewer" data-lenis-prevent>
                    <div class="pdfContainer">
                        <div class="pdfPlaceholder">
                            <div class="placeholderContent">
                                <div class="placeholderIcon">📄</div>
                                <h3>Select a menu to view</h3>
                                <p>Choose a menu from the list to view the PDF here</p>
                            </div>
                        </div>
                        
                        <iframe class="pdfFrame" style="display: none;" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
        </div>
        
        <?php else: ?>
        <div class="noMenus">
            <p>No menus have been added yet. Please add menus in the <a href="<?php echo admin_url('admin.php?page=menus-settings'); ?>">Menus settings</a>.</p>
        </div>
        <?php endif; ?>
        
    </div>
</section>

<script>
// Initialize PDF Menus Block functionality
document.addEventListener('DOMContentLoaded', function() {
    initPdfMenusBlock('<?php echo $block_id; ?>');
});
</script>
