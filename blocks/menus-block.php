<?php
$size = 'large'; // (thumbnail, medium, large, full or custom size)

$menus = get_posts(array(
    'posts_per_page' => '-1',
    'order' => 'ASC',
    'post_type'     => 'menu',
));
$the_query = new WP_Query( $menus );
$title = get_field('title');
$menus_length = count($menus);
?>

<section class="menusBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper smaller">
    <?php if( $menus ): ?>
      <div class="menus">
        <div class="bigLogoWrapper">
          <?php include("parts/big_logo.php");  ?>
        </div>
        <?php if ($menus_length > 3): ?>
           <div class="sliderWrapper">
            <div class="slider" data-slider>
        <?php endif; ?>
        <?php foreach( $menus as $post ): setup_postdata($post);
          $menuLink = "";
          $title = get_field( 'title', $post->ID );
          $image = get_field( 'image', $post->ID );
          $menu = get_field( 'menu_image', $post->ID );
          $video = get_field( 'video', $post->ID );
          $menuLink = $menu['url'];
          ?>
          <a href="<?php echo esc_url($menuLink); ?>" target="_blank" title="<?php echo esc_html( $title ); ?>" class="slide menu">
            <span class="cols">
              <span class="bigTitle"><strong><?php echo esc_html( $title ); ?></strong></span>
              <span class="textLink"><span class="innerText"><?php the_field("link_label"); ?></span><span class="arrow"></span></span>
            </span>
            <span class="imageWrapper">
              <span class="innerImage">
                <?php if ($video): ?>
                  <video poster="<?php echo esc_url($image['url']); ?>" class="video" muted playsinline loop autoplay>
                    <source src="<?php echo esc_url($video); ?>" type="video/mp4">
                  </video>
                <?php elseif( $image ): ?>
                  <img class="lazy" data-src="<?php echo esc_url($image["sizes"]['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" />
                <?php endif; ?>
              </span>
            </span>
          </a>
        <?php endforeach; ?>
        <?php wp_reset_postdata(); ?>
        <?php if ($menus_length > 3): ?>
            </div>
            <div class="sliderButton prev" data-prev><?php include("parts/slider_button.php");  ?></div>
            <div class="sliderButton next" data-next><?php include("parts/slider_button.php");  ?></div>
            <div class="sliderIndicator"><div class="innerBar"></div></div>
          </div>
        <?php endif; ?>
      </div>
    <?php endif; ?>
  </div>
</section>
