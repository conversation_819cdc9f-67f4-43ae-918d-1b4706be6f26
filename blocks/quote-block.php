<?php
$size = 'full';
$image = get_field("image");
$pattern = '/(.*)\*(.*)\*(.*)/';
$replacement = '$1<span class="primary">$2</span>$3';
?>

<section class="quoteBlock<?php if (get_field('no_margin_bottom')): ?> noMarginBottom<?php endif; ?>
  <?php if (get_field('no_margin_top')): ?> noMarginTop<?php endif; ?><?php if (get_field('no_radius_top')): ?> noBorderTop<?php endif; ?>
  <?php if (get_field('no_radius_bottom')): ?> noBorderBottom<?php endif; ?>" data-init>
  <div class="contentWrapper small">
    <div class="titleWrapper">
      <h2 class="bigTitle splitThis" data-init data-split><?php the_field("quote") ?></h2>
    </div>
    <div class="signatureWrapper" data-scroll data-scroll-speed="2">
      <h3 class="signature primary"><?php the_field("signature") ?></h3>
    </div>
  </div>
</section>
