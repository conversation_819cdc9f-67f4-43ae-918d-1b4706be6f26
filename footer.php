
<footer class="footer">
  <div class="contentWrapper">
    <div class="topFooter">
      <div class="cols">
        <div class="col">
          <h3 class="title">Newsletter Signup</h3>
          <div class="text white">
            <p>Keep up to date with our menu and events.</p>
          </div>
          <div class="formWrapper">
            <?php echo do_shortcode(get_theme_mod('newsletter_form')); ?>
          </div>
        </div>
        <div class="col">
          <h3 class="title">Location</h3>
          <div class="text white">
            <p><a class="link" title="<?php echo esc_html(get_theme_mod('customTheme-main-callout-address')); ?>" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-maps-link')); ?>" target="_blank"><?php echo esc_html(get_theme_mod('customTheme-main-callout-address')); ?></a></p>
          </div>
        </div>
        <div class="col">
          <h3 class="title">Hours</h3>
          <div class="text white">
             <?php echo wpautop(get_theme_mod('customTheme-main-callout-contact-text')); ?>
          </div>
        </div>
      </div>
    </div>
    <div class="middleFooter">
      <div class="cols">
        <div class="col">
          <svg data-name="Group 177" width="1077.42" height="324.722" viewBox="0 0 1077.42 324.722">
            <path data-name="Path 6" d="M0,2.4V54.712H52.094V320.175h56.87V54.712h52.095V2.4Z" transform="translate(0 1.073)" fill="#f5f1ea"/>
            <path data-name="Path 7" d="M242.754,132.635H186.535V2.4H129.449V320.175h57.086V184.73h56.219V320.175h57.086V2.4H242.754Z" transform="translate(57.88 1.073)" fill="#f5f1ea"/>
            <path data-name="Path 8" d="M274.2,320.175H412.465V267.863H331.286V187.625h69.893V132.635H331.286V54.712h81.179V2.4H274.2Z" transform="translate(122.6 1.073)" fill="#f5f1ea"/>
            <path data-name="Path 9" d="M447.692,320.175H504.78V187.625h69.893V132.635H504.78V54.712h81.181V2.4H447.692Z" transform="translate(200.174 1.073)" fill="#f5f1ea"/>
            <rect data-name="Rectangle 46" width="57.088" height="317.775" transform="translate(819.636 3.473)" fill="#f5f1ea"/>
            <path data-name="Path 10" d="M705.278,188.7h36.177v45.511c0,18.684-3.744,26.257-9.364,31.877-4.256,4.255-9.57,6.324-16.248,6.324-7.806,0-14.9-2.01-19.215-6.325-11.8-11.8-12.4-47.1-12.4-103.723s.6-91.921,12.4-103.724c4.317-4.315,9.962-6.325,17.768-6.325,12.046,0,24.508,4.4,25.615,37.046l.142,4.194h56.86l-.136-4.472C795.211,34.133,763.608,0,714.4,0c-24.189,0-44,7.665-58.853,22.76-25.956,25.956-28.4,58.9-28.4,139.6S629.584,276,655.531,301.95c14.878,15.111,34.683,22.772,58.865,22.772,23.526,0,43.719-7.636,58.42-22.109,15.966-15.966,24.063-38.763,24.063-64.86V133.708h-91.6Z" transform="translate(280.408 0)" fill="#f5f1ea"/>
          </svg>

        </div>
        <div class="col">
          <div class="socials">
            <?php include("blocks/parts/socials.php"); ?>
          </div>
            <?php if (get_theme_mod('customTheme-main-callout-tripadvisor')) : ?>
              <a class="tripAdvisor" href="<?php echo esc_html(get_theme_mod('customTheme-main-callout-tripadvisor')); ?>" target="_blank">
                <img src="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-tripadvisor-image'))); ?>" alt="Tripadvisor" />
              </a>
            <?php endif; ?>
        </div>
      </div>
    </div>
    <div class="divider"></div>
    <div class="bottomFooter">
      <div class="items">
        <div class="item text white">
          <?php $year = date('Y'); ?>
          <p>Copyright The Fig <?php echo $year; ?></p>
        </div>
        <div class="innerDivider"></div>
        <div class="item text white">
            <p><a class="link" href="/privacy-cookie-policy/">Privacy & Cookie Policy</a></p>
        </div>
        <div class="innerDivider"></div>
        <div class="item text white">
          <p>Website by <a class="link" href="https://www.bforbroady.com/" target="_blank">BforBroady</a></p>
        </div>
      </div>
    </div>
  </div>
</footer>
      </div> <!-- End .blocks -->
    </div> <!-- End #pageContainer -->

    <?php
    // Include the popup template
    get_template_part('template-parts/popup');

    // Include the booking popup template
    get_template_part('template-parts/booking-popup');

    // Include the sticky WhatsApp button
    get_template_part('template-parts/sticky-whatsapp');
    ?>

    <?php wp_footer(); ?>
  </body>
</html>
