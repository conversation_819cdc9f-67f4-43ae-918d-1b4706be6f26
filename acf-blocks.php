<?php
/**
 * ACF Blocks Configuration
 * 
 * This file manages all ACF blocks for the theme.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ACF Blocks Manager Class
 */
class ACF_Blocks_Manager {
    
    public function __construct() {
        add_action('acf/init', array($this, 'register_blocks'));
        add_action('acf/init', array($this, 'register_block_fields'));
    }
    
    /**
     * Register all ACF blocks
     */
    public function register_blocks() {
        // Check if ACF function exists
        if (!function_exists('acf_register_block_type')) {
            return;
        }

        // Register Home Header block
        $this->register_home_header_block();
        $this->register_image_text_block();
        $this->register_text_block();
        $this->register_sticky_big_media_block();
        $this->register_images_text_block();
        $this->register_rooms_marquee_block();
        $this->register_rooms_overview_block();
        $this->register_bigger_image_text_block();
        $this->register_gift_card_block();
        $this->register_articles_block();
        $this->register_partners_marquee_block();
        $this->register_faq_block();
        $this->register_four_images_text_block();
        $this->register_header_block();
        $this->register_images_slider_block();
        $this->register_big_header_block();
        $this->register_contact_block();
        $this->register_pdf_menus_block();
        $this->register_two_images_block();
        $this->register_details_block();
    }

    /**
     * Register Home Header block
     */
    private function register_home_header_block() {
        acf_register_block_type(array(
            'name'              => 'home_header_block',
            'title'             => __('Home Header Block'),
            'render_template'   => 'blocks/home-header-block.php',
            'category'          => 'headers',
            'icon'              => 'cover-image',
            'keywords'          => array('header', 'home', 'hero', 'banner'),
            'supports'          => array(
                'align' => false,
                'anchor' => true,
                'mode' => false,
            ),
            'mode'              => 'edit',
            'example'           => array(
                'attributes' => array(
                    'mode' => 'preview',
                    'data' => array(
                        'main_title' => 'Welcome to *Our Restaurant*',
                        'media_type' => 'image',
                    )
                )
            ),
        ));
    }

    // image text block:
    private function register_image_text_block() {
        acf_register_block_type(array(
            'name'              => 'image_text_block',
            'title'             => __('Image Text Block'),
            'render_template'   => 'blocks/image-text-block.php',
            'category'          => 'formatting',
            'icon'              => 'cover-image',
            'keywords'          => array('image', 'text', 'content', 'block'),
            'supports'          => array(
                'align' => false,
                'anchor' => true,
                'mode' => false,
            ),
            'mode'              => 'edit',
            'example'           => array(
                'attributes' => array(
                    'mode' => 'preview',
                    'data' => array(
                        'main_title' => 'Welcome to *Our Restaurant*',
                        'media_type' => 'image',
                    )
                )
            ),
        ));
    }

    // text block:
    private function register_text_block() {
        acf_register_block_type(array(
            'name'              => 'text_block',
            'title'             => __('Text Block'),
            'render_template'   => 'blocks/text-block.php',
            'category'          => 'formatting',
            'icon'              => 'editor-textcolor',
            'keywords'          => array('text', 'content', 'block'),
            'supports'          => array(
                'align' => false,
                'anchor' => true,
                'mode' => false,
            ),
            'mode'              => 'edit',
            'example'           => array(
                'attributes' => array(
                    'mode' => 'preview',
                    'data' => array(
                        'main_title' => 'Welcome to *Our Restaurant*',
                        'media_type' => 'image',
                    )
                )
            ),
        ));
    }
    private function register_sticky_big_media_block() {
        acf_register_block_type(array(
            'name'              => 'sticky_big_media_block',
            'title'             => __('Sticky Big Media Block'),
            'render_template'   => 'blocks/sticky-big-media-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'sticky', 'big', 'media' ),
        ));
    }
    private function register_images_text_block() {
        acf_register_block_type(array(
            'name'              => 'images_text_block',
            'title'             => __('Images Text Block'),
            'render_template'   => 'blocks/images-text-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'images', 'text' ),
        ));
    }
    private function register_rooms_marquee_block() {
        acf_register_block_type(array(
            'name'              => 'rooms_marquee_block',
            'title'             => __('Rooms Marquee Block'),
            'render_template'   => 'blocks/rooms-marquee-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'rooms', 'marquee' ),
        ));
    }

    private function register_rooms_overview_block() {
        acf_register_block_type(array(
            'name'              => 'rooms_overview_block',
            'title'             => __('Rooms Overview Block'),
            'render_template'   => 'blocks/rooms-overview-block.php',
            'category'          => 'rooms',
            'icon'              => 'building',
            'keywords'          => array( 'rooms', 'overview', 'all' ),
            'supports'          => array(
                'align' => false,
                'anchor' => true,
                'mode' => false,
            ),
            'mode'              => 'edit',
        ));
    }
    private function register_bigger_image_text_block() {
        acf_register_block_type(array(
            'name'              => 'bigger_image_text_block',
            'title'             => __('Bigger Image Text Block'),
            'render_template'   => 'blocks/bigger-image-text-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'bigger', 'image', 'text' ),
        ));
    }
    private function register_gift_card_block() {
        acf_register_block_type(array(
            'name'              => 'gift_card_block',
            'title'             => __('Gift Card Block'),
            'render_template'   => 'blocks/gift-card-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'gift', 'card' ),
        ));
    }

    private function register_articles_block() {
        acf_register_block_type(array(
            'name'              => 'articles_block',
            'title'             => __('Articles Block'),
            'render_template'   => 'blocks/articles-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'articles', 'block' ),
        ));
    }
    private function register_partners_marquee_block() {
        acf_register_block_type(array(
            'name'              => 'partners_marquee_block',
            'title'             => __('Partners Marquee Block'),
            'render_template'   => 'blocks/partners-marquee-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'partners', 'marquee' ),
        ));
    }
    private function register_faq_block() {
        acf_register_block_type(array(
            'name'              => 'faq_block',
            'title'             => __('FAQ Block'),
            'render_template'   => 'blocks/faq-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'faq', 'block' ),
        ));
    }
    private function register_four_images_text_block() {
        acf_register_block_type(array(
            'name'              => 'four_images_text_block',
            'title'             => __('Four Images Text Block'),
            'render_template'   => 'blocks/four-images-text-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'four', 'images', 'text' ),
        ));
    }
    private function register_header_block() {
        acf_register_block_type(array(
            'name'              => 'header_block',
            'title'             => __('Header Block'),
            'render_template'   => 'blocks/header-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'header', 'block' ),
        ));
    }
    private function register_images_slider_block() {
        acf_register_block_type(array(
            'name'              => 'images_slider_block',
            'title'             => __('Images Slider Block'),
            'render_template'   => 'blocks/images-slider-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'images', 'slider' ),
        ));
    }
    private function register_big_header_block() {
        acf_register_block_type(array(
            'name'              => 'big_header_block',
            'title'             => __('Big Header Block'),
            'render_template'   => 'blocks/big-header-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'big', 'header' ),
        ));
    }
    private function register_contact_block() {
        acf_register_block_type(array(
            'name'              => 'contact_block',
            'title'             => __('Contact Block'),
            'render_template'   => 'blocks/contact-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'contact', 'block' ),
        ));
    }
    private function register_two_images_block() {
        acf_register_block_type(array(
            'name'              => 'two_images_block',
            'title'             => __('Two Images Block'),
            'render_template'   => 'blocks/two-images-block.php',
            'category'          => 'formatting',
            'icon'              => 'format-image',
            'keywords'          => array( 'two', 'images' ),
        ));
    }

    /**
     * Register Details Block
     */
    private function register_details_block() {
        acf_register_block_type(array(
            'name'              => 'details_block',
            'title'             => __('Details Block'),
            'render_template'   => 'blocks/details-block.php',
            'category'          => 'formatting',
            'icon'              => 'list-view',
            'keywords'          => array( 'details', 'columns', 'text' ),
        ));
    }

    /**
     * Register all ACF field groups for blocks
     */
    public function register_block_fields() {
        if (!function_exists('acf_add_local_field_group')) {
            return;
        }

        $this->register_home_header_block_fields();
        $this->register_image_text_block_fields();
        $this->register_text_block_fields();
        $this->register_sticky_big_media_block_fields();
        $this->register_images_text_block_fields();
        $this->register_rooms_marquee_block_fields();
        $this->register_rooms_overview_block_fields();
        $this->register_bigger_image_text_block_fields();
        $this->register_gift_card_block_fields();
        $this->register_articles_block_fields();
        $this->register_faq_block_fields();
        $this->register_four_images_text_block_fields();
        $this->register_header_block_fields();
        $this->register_images_slider_block_fields();
        $this->register_big_header_block_fields();
        $this->register_contact_block_fields();
        $this->register_pdf_menus_block_fields();
        $this->register_two_images_block_fields();
        $this->register_details_block_fields();
    }

    /**
     * Register Home Header Block field group
     */
    private function register_home_header_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_home_header_block', 
            'title' => 'Home Header Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_home_header_media_type',
                    'label' => 'Media Type',
                    'name' => 'media_type',
                    'type' => 'radio',
                    'instructions' => 'Choose between image or video for the header background',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '50',
                        'class' => '',
                        'id' => '',
                    ),
                    'choices' => array(
                        'image' => 'Image',
                        'video' => 'Video',
                    ),
                    'allow_null' => 0,
                    'other_choice' => 0,
                    'default_value' => 'image',
                    'layout' => 'horizontal',
                    'return_format' => 'value',
                    'save_other_choice' => 0,
                ),
                array(
                    'key' => 'field_home_header_image',
                    'label' => 'Header Image',
                    'name' => 'header_image',
                    'type' => 'image',
                    'instructions' => 'Upload the header background image',
                    'required' => 1,
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_home_header_media_type',
                                'operator' => '==',
                                'value' => 'image',
                            ),
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '50',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'library' => 'all',
                    'min_width' => 1200,
                    'min_height' => 600,
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => 'jpg,jpeg,png,webp',
                ),
                array(
                    'key' => 'field_home_header_video',
                    'label' => 'Header Video',
                    'name' => 'header_video',
                    'type' => 'file',
                    'instructions' => 'Upload the header background video (MP4 format recommended)',
                    'required' => 1,
                    'conditional_logic' => array(
                        array(
                            array(
                                'field' => 'field_home_header_media_type',
                                'operator' => '==',
                                'value' => 'video',
                            ),
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '50',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'library' => 'all',
                    'min_size' => '',
                    'max_size' => '50MB',
                    'mime_types' => 'mp4,webm,mov',
                ),
                array(
                    'key' => 'field_home_header_title',
                    'label' => 'Main Title',
                    'name' => 'main_title',
                    'type' => 'textarea',
                    'instructions' => 'Enter the main title for the header. Use * around text to highlight it.',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Welcome to *Our Restaurant*',
                    'maxlength' => 200,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_home_header_location_title',
                    'label' => 'Location Title',
                    'name' => 'location_title',
                    'type' => 'text',
                    'instructions' => 'Title for the location section',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => 'Location',
                    'placeholder' => 'Location',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 50,
                ),
                array(
                    'key' => 'field_home_header_hours_title',
                    'label' => 'Hours Title',
                    'name' => 'hours_title',
                    'type' => 'text',
                    'instructions' => 'Title for the opening hours section',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => 'Opening Hours',
                    'placeholder' => 'Opening Hours',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 50,
                ),
                array(
                    'key' => 'field_home_header_hours_text',
                    'label' => 'Hours Text',
                    'name' => 'hours_text',
                    'type' => 'textarea',
                    'instructions' => 'Enter the opening hours text',   
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Opening Hours Text',
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_home_header_contact_title',
                    'label' => 'Contact Title',
                    'name' => 'contact_title',
                    'type' => 'text',
                    'instructions' => 'Title for the contact section',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => 'Contact',
                    'placeholder' => 'Contact',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 50,
                ),
                array(
                    'key' => 'field_home_header_contact_text',
                    'label' => 'Contact Text',
                    'name' => 'contact_text',
                    'type' => 'textarea',
                    'instructions' => 'Enter the contact information text',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Call us at +44 ************ <NAME_EMAIL>',
                    'maxlength' => 300,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_home_header_contact_link',
                    'label' => 'Contact Link',
                    'name' => 'contact_link',
                    'type' => 'link',
                    'instructions' => 'Link for the contact section (e.g., contact page, phone number, email)',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                ),
                array(
                    'key' => 'field_home_header_location_link',
                    'label' => 'Location Link',
                    'name' => 'location_link',
                    'type' => 'link',
                    'instructions' => 'Link for the location section (e.g., Google Maps, directions)',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                ),
                array(
                    'key' => 'field_home_header_hours_link',
                    'label' => 'Hours Link',
                    'name' => 'hours_link',
                    'type' => 'link',
                    'instructions' => 'Link for the hours section (e.g., reservations, opening hours page)',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/home-header-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Home Header block',
            'show_in_rest' => 0,
        ));
    }

    private function register_image_text_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_image_text_block',
            'title' => 'Image Text Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_image_text_block_title',
                    'label' => 'Title',
                    'name' => 'title',
                    'type' => 'textarea',
                    'instructions' => 'Enter the title for the block. Use * around text to highlight it.',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Welcome to *Our Restaurant*',
                    'maxlength' => 200,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_image_text_block_subtitle',
                    'label' => 'Subtitle',
                    'name' => 'subtitle',
                    'type' => 'text',
                    'instructions' => 'Enter the subtitle for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Subtitle',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 50,
                ),
                array(
                    'key' => 'field_image_text_block_text',
                    'label' => 'Text',
                    'name' => 'text',
                    'type' => 'textarea',   
                    'instructions' => 'Enter the text for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Text',
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_image_text_block_image',
                    'label' => 'Image',
                    'name' => 'image',
                    'type' => 'image',
                    'instructions' => 'Upload an image for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
                array(
                    'key' => 'field_image_text_block_image_right',
                    'label' => 'Image on the right',
                    'name' => 'image_right',
                    'type' => 'true_false',
                    'instructions' => 'Show the image on the right side',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 0,
                    'ui' => 1,
                    'ui_on_text' => 'Yes',
                    'ui_off_text' => 'No',
                ),
                array(
                    'key' => 'field_image_text_block_link',
                    'label' => 'Link',
                    'name' => 'link',
                    'type' => 'link',
                    'instructions' => 'Link for the button',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                ),
                array(
                    'key' => 'field_image_text_block_link_2',
                    'label' => 'Link 2',
                    'name' => 'link_2',
                    'type' => 'link',
                    'instructions' => 'Second link for the button',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                ),
                array(
                    'key' => 'field_image_text_block_extra_text',
                    'label' => 'Extra Text',
                    'name' => 'extra_text',
                    'type' => 'textarea',
                    'instructions' => 'Extra text to show next to the button',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Extra text',
                    'maxlength' => '',
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/image-text-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Image Text block',
            'show_in_rest' => 0,
        ));
    }

    /**
     * Register Text Block field group
     */
    private function register_text_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_text_block',
            'title' => 'Text Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_text_block_title',
                    'label' => 'Title',
                    'name' => 'title',
                    'type' => 'textarea',
                    'instructions' => 'Enter the title for the block. Use * around text to highlight it.',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Welcome to *Our Restaurant*',
                    'maxlength' => 200,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_text_block_subtitle',
                    'label' => 'Subtitle',
                    'name' => 'subtitle',
                    'type' => 'text',
                    'instructions' => 'Enter the subtitle for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Subtitle',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 50,
                ),
                array(
                    'key' => 'field_text_block_text',
                    'label' => 'Text',
                    'name' => 'text',
                    'type' => 'textarea',
                    'instructions' => 'Enter the text for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Text',
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/text-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Text block',
            'show_in_rest' => 0,
        ));
    }

    /**
     * Register Sticky Big Media Block field group
     */
    private function register_sticky_big_media_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_sticky_big_media_block',
            'title' => 'Sticky Big Media Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_sticky_big_media_block_images',
                    'label' => 'Images',
                    'name' => 'images',
                    'type' => 'gallery',
                    'instructions' => 'Upload multiple images for the slideshow. If you add multiple images, they will automatically rotate.',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'insert' => 'append',
                    'library' => 'all',
                    'min' => 0,
                    'max' => '',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/sticky-big-media-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Sticky Big Media block',
            'show_in_rest' => 0,
        ));
    }
    private function register_images_text_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_images_text_block',
            'title' => 'Images Text Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_images_text_block_group',
                    'label' => 'Items',
                    'name' => 'items',
                    'type' => 'repeater',
                    'instructions' => 'Add items for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'collapsed' => '',
                    'min' => 0,
                    'max' => 0,
                    'layout' => 'block',
                    'button_label' => 'Add Item',
                    'sub_fields' => array(
                        array(
                            'key' => 'field_images_text_block_image',
                            'label' => 'Image',
                            'name' => 'image',
                            'type' => 'image',
                            'instructions' => 'Upload an image for the block',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'return_format' => 'array',
                            'preview_size' => 'medium',
                            'library' => 'all',
                            'min_width' => '',
                            'min_height' => '',
                            'min_size' => '',
                            'max_width' => '',
                            'max_height' => '',
                            'max_size' => '',
                            'mime_types' => '',
                        ),
                        array(
                            'key' => 'field_images_text_block_title',
                            'label' => 'Title',
                            'name' => 'title',
                            'type' => 'textarea',
                            'instructions' => 'Enter the title for the block. Use * around text to highlight it.',
                            'required' => 1,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => 'Welcome to *Our Restaurant*',
                            'maxlength' => 200,
                            'rows' => 3,
                            'new_lines' => 'br',
                        ),
                        array(
                            'key' => 'field_images_text_block_text',
                            'label' => 'Text',
                            'name' => 'text',
                            'type' => 'textarea',
                            'instructions' => 'Enter the text for the block',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => 'Text',
                            'rows' => 3,
                            'new_lines' => 'br',
                        ),
                    ),
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/images-text-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Images Text block',
            'show_in_rest' => 0,
        ));
    }
    // rooms marquee block
    private function register_rooms_marquee_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_rooms_marquee_block',
            'title' => 'Rooms Marquee Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_rooms_marquee_block_link',
                    'label' => 'Link',
                    'name' => 'link',
                    'type' => 'link',
                    'instructions' => 'Link for the text below the marquee',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/rooms-marquee-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Rooms Marquee block',
            'show_in_rest' => 0,
        ));
    }

    // rooms overview block
    private function register_rooms_overview_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_rooms_overview_block',
            'title' => 'Rooms Overview Block Settings',
            'fields' => array(),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/rooms-overview-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Rooms Overview block',
            'show_in_rest' => 0,
        ));
    }
    // bigger image text block
    private function register_bigger_image_text_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_bigger_image_text_block',
            'title' => 'Bigger Image Text Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_bigger_image_text_block_image',
                    'label' => 'Image',
                    'name' => 'image',
                    'type' => 'image',
                    'instructions' => 'Upload an image for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
                array(
                    'key' => 'field_bigger_image_text_block_title',
                    'label' => 'Title',
                    'name' => 'title',
                    'type' => 'textarea',
                    'instructions' => 'Enter the title for the block. Use * around text to highlight it.',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Welcome to *Our Restaurant*',
                    'maxlength' => 200,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_bigger_image_text_block_text',
                    'label' => 'Text',
                    'name' => 'text',
                    'type' => 'textarea',
                    'instructions' => 'Enter the text for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Text',
                    'maxlength' => 300,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/bigger-image-text-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Bigger Image Text block',
            'show_in_rest' => 0,
        ));
    }
    // gift card block
    private function register_gift_card_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_gift_card_block',
            'title' => 'Gift Card Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_gift_card_block_image',
                    'label' => 'Image',
                    'name' => 'image',
                    'type' => 'image',
                    'instructions' => 'Upload an image for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
                array(
                    'key' => 'field_gift_card_block_title',
                    'label' => 'Title',
                    'name' => 'title',
                    'type' => 'textarea',
                    'instructions' => 'Enter the title for the block. Use * around text to highlight it.',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Welcome to *Our Restaurant*',
                    'maxlength' => 200,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_gift_card_block_text',
                    'label' => 'Text',
                    'name' => 'text',
                    'type' => 'textarea',
                    'instructions' => 'Enter the text for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Text',
                    'maxlength' => 300,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_gift_card_block_link',
                    'label' => 'Link',
                    'name' => 'link',
                    'type' => 'link',
                    'instructions' => 'Link for the button',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/gift-card-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Gift Card block',
            'show_in_rest' => 0,
        ));
    }
    private function register_articles_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_articles_block',
            'title' => 'Articles Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_articles_block_title',
                    'label' => 'Title',
                    'name' => 'title',
                    'type' => 'textarea',
                    'instructions' => 'Enter the title for the block. Use * around text to highlight it.',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Welcome to *Our Restaurant*',
                    'maxlength' => 200,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_articles_block_text',
                    'label' => 'Text',
                    'name' => 'text',
                    'type' => 'textarea',
                    'instructions' => 'Enter the text for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Text',
                    'maxlength' => 300,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_articles_block_link',
                    'label' => 'Link',
                    'name' => 'link',
                    'type' => 'link',
                    'instructions' => 'Link for the button',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                ),
                // articles as group (with title, image, link , text init:)
                array(
                    'key' => 'field_articles_block_articles',
                    'label' => 'Articles',
                    'name' => 'articles',
                    'type' => 'repeater',
                    'instructions' => 'Add articles for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'collapsed' => '',
                    'min' => 0,
                    'max' => 0,
                    'layout' => 'block',
                    'button_label' => 'Add Article',
                    'sub_fields' => array(
                        array(
                            'key' => 'field_articles_block_articles_image',
                            'label' => 'Image',
                            'name' => 'image',
                            'type' => 'image',
                            'instructions' => 'Upload an image for the article',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'return_format' => 'array',
                            'preview_size' => 'medium',
                            'library' => 'all',
                            'min_width' => '',
                            'min_height' => '',
                            'min_size' => '',
                            'max_width' => '',
                            'max_height' => '',
                            'max_size' => '',
                            'mime_types' => '',
                        ),
                        array(
                            'key' => 'field_articles_block_articles_title',
                            'label' => 'Title',
                            'name' => 'title',
                            'type' => 'text',
                            'instructions' => 'Enter the title for the article',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => 'Title',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => 50,
                        ),
                        array(
                            'key' => 'field_articles_block_articles_link',
                            'label' => 'Link',
                            'name' => 'link',
                            'type' => 'link',
                            'instructions' => 'Link for the article',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'return_format' => 'array',
                        ),
                    ),
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/articles-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Articles block',
            'show_in_rest' => 0,
        ));
        
    }
    // FAQ block (title, text, faq-item with question, answer)
    private function register_faq_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_faq_block',
            'title' => 'FAQ Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_faq_block_title',
                    'label' => 'Title',
                    'name' => 'title',
                    'type' => 'textarea',
                    'instructions' => 'Enter the title for the block. Use * around text to highlight it.',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Welcome to *Our Restaurant*',
                    'maxlength' => 200,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_faq_block_text',
                    'label' => 'Text',
                    'name' => 'text',
                    'type' => 'textarea',   
                    'instructions' => 'Enter the text for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Text',
                    'maxlength' => 300,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_faq_block_faq_items',
                    'label' => 'FAQ Items',
                    'name' => 'faq_items',
                    'type' => 'repeater',
                    'instructions' => 'Add FAQ items for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'collapsed' => '',
                    'min' => 0,
                    'max' => 0,
                    'layout' => 'block',
                    'button_label' => 'Add FAQ Item',
                    'sub_fields' => array(
                        array(
                            'key' => 'field_faq_block_faq_items_question',
                            'label' => 'Question',
                            'name' => 'question',
                            'type' => 'text',
                            'instructions' => 'Enter the question for the FAQ item',
                            'required' => 1,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => 'Question',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => 100,
                        ),
                        array(
                            'key' => 'field_faq_block_faq_items_answer',
                            'label' => 'Answer',
                            'name' => 'answer',
                            'type' => 'textarea',
                            'instructions' => 'Enter the answer for the FAQ item',
                            'required' => 1,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => 'Answer',
                            'maxlength' => 300,
                            'rows' => 3,
                            'new_lines' => 'br',
                        ),
                    ),
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/faq-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the FAQ block',
            'show_in_rest' => 0,
        ));
    }
    // four images text block:
    private function register_four_images_text_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_four_images_text_block',
            'title' => 'Four Images Text Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_four_images_text_block_title',
                    'label' => 'Title',
                    'name' => 'title',
                    'type' => 'textarea',
                    'instructions' => 'Enter the title for the block. Use * around text to highlight it.',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Welcome to *Our Restaurant*',
                    'maxlength' => 200,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_four_images_text_block_text',
                    'label' => 'Text',
                    'name' => 'text',
                    'type' => 'textarea',   
                    'instructions' => 'Enter the text for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Text',
                    'maxlength' => 300,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_four_images_text_block_items',
                    'label' => 'Items',
                    'name' => 'items',
                    'type' => 'repeater',
                    'instructions' => 'Add items for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'collapsed' => '',
                    'min' => 0,
                    'max' => 0,
                    'layout' => 'block',
                    'button_label' => 'Add Item',
                    'sub_fields' => array(
                        array(
                            'key' => 'field_four_images_text_block_items_title',
                            'label' => 'Title',
                            'name' => 'title',
                            'type' => 'text',
                            'instructions' => 'Enter the title for the item',
                            'required' => 1,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'default_value' => '',
                            'placeholder' => 'Title',
                            'prepend' => '',
                            'append' => '',
                            'maxlength' => 50,
                        ),
                        array(
                            'key' => 'field_four_images_text_block_items_image',
                            'label' => 'Image',
                            'name' => 'image',
                            'type' => 'image',
                            'instructions' => 'Upload an image for the item',
                            'required' => 0,
                            'conditional_logic' => 0,
                            'wrapper' => array(
                                'width' => '',
                                'class' => '',
                                'id' => '',
                            ),
                            'return_format' => 'array',
                            'preview_size' => 'medium',
                            'library' => 'all',
                            'min_width' => '',
                            'min_height' => '',
                            'min_size' => '',
                            'max_width' => '',
                            'max_height' => '',
                            'max_size' => '',
                            'mime_types' => '',
                        ),
                    ),
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/four-images-text-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Four Images Text block',
            'show_in_rest' => 0,
        ));
    }
    // header block
    private function register_header_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_header_block',
            'title' => 'Header Block Settings',
            'fields' => array(
                array(
                    // 'key' => 'field_header_block_title',
                    // 'label' => 'Title',
                    // 'name' => 'title',
                    // 'type' => 'textarea',
                    // 'instructions' => 'Enter the title for the block. Use * around text to highlight it.',
                    // 'required' => 1,
                    // 'conditional_logic' => 0,
                    // 'wrapper' => array(
                    //     'width' => '',
                    //     'class' => '',
                    //     'id' => '',
                    // ),
                    'default_value' => '',
                    'placeholder' => 'Welcome to *Our Restaurant*',
                    'maxlength' => 200,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_header_block_image',
                    'label' => 'Image',
                    'name' => 'image',
                    'type' => 'image',
                    'instructions' => 'Upload an image for the block',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/header-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Header block',
            'show_in_rest' => 0,
        ));
    }
    // images slider block
    private function register_images_slider_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_images_slider_block',
            'title' => 'Images Slider Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_images_slider_block_images',
                    'label' => 'Images',
                    'name' => 'images',
                    'type' => 'gallery',
                    'instructions' => 'Upload multiple images for the slideshow. If you add multiple images, they will automatically rotate.',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'insert' => 'append',
                    'library' => 'all',
                    'min' => 0,
                    'max' => '',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/images-slider-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Images Slider block',
            'show_in_rest' => 0,
        ));
    }
    private function register_big_header_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_big_header_block',
            'title' => 'Big Header Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_big_header_block_title',
                    'label' => 'Title',
                    'name' => 'title',
                    'type' => 'text',
                    'instructions' => 'Enter the title for the block',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Title',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 100,
                ),
                array(
                    'key' => 'field_big_header_block_image',
                    'label' => 'Image',
                    'name' => 'image',
                    'type' => 'image',
                    'instructions' => 'Upload an image for the block',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/big-header-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Big Header block',
            'show_in_rest' => 0,
        ));
    }
    private function register_contact_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_contact_block',
            'title' => 'Contact Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_contact_block_title',
                    'label' => 'Title',
                    'name' => 'title',
                    'type' => 'text',
                    'instructions' => 'Enter the title for the block',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Title',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 100,
                ),
                array(
                    'key' => 'field_contact_block_image',
                    'label' => 'Image',
                    'name' => 'image',
                    'type' => 'image',
                    'instructions' => 'Upload an image for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
                array(
                    'key' => 'field_contact_block_form',
                    'label' => 'Form Embed',
                    'name' => 'form',
                    'type' => 'textarea',
                    'instructions' => 'Paste the form embed code here',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Form Embed',
                    'maxlength' => '',
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_contact_block_subtitle',
                    'label' => 'Subtitle',
                    'name' => 'subtitle',
                    'type' => 'text',
                    'instructions' => 'Enter the subtitle for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Subtitle',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 100,
                ),
                // location title
                array(
                    'key' => 'field_contact_block_location_title',
                    'label' => 'Location Title',
                    'name' => 'location_title',
                    'type' => 'text',
                    'instructions' => 'Enter the location title for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Location Title',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 100,
                ),
                // location text
                array(
                    'key' => 'field_contact_block_location_text',
                    'label' => 'Location Text',
                    'name' => 'location_text',
                    'type' => 'textarea',
                    'instructions' => 'Enter the location text for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Location Text',
                    'maxlength' => 300,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                // hours title
                array(
                    'key' => 'field_contact_block_hours_title',
                    'label' => 'Hours Title',
                    'name' => 'hours_title',
                    'type' => 'text',
                    'instructions' => 'Enter the hours title for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Hours Title',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 100,
                ),
                // hours text
                array(
                    'key' => 'field_contact_block_hours_text',
                    'label' => 'Hours Text',
                    'name' => 'hours_text',
                    'type' => 'textarea',
                    'instructions' => 'Enter the hours text for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Hours Text',
                    'maxlength' => 300,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_contact_block_text',
                    'label' => 'Text',
                    'name' => 'text',
                    'type' => 'textarea',
                    'instructions' => 'Enter the text for the block',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Text',
                    'maxlength' => 300,
                    'rows' => 3,
                    'new_lines' => 'br',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/contact-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Contact block',
            'show_in_rest' => 0,
        ));
    }

    /**
     * Register PDF Menus block
     */
    private function register_pdf_menus_block() {
        acf_register_block_type(array(
            'name'              => 'pdf_menus_block',
            'title'             => __('PDF Menus Block'),
            'render_template'   => 'blocks/pdf-menus-block.php',
            'category'          => 'formatting',
            'icon'              => 'media-document',
            'keywords'          => array('menu', 'pdf', 'download', 'restaurant'),
            'supports'          => array(
                'align' => false,
                'anchor' => true,
                'mode' => false,
            ),
            'mode'              => 'edit',
            'example'           => array(
                'attributes' => array(
                    'mode' => 'preview',
                    'data' => array(
                        'title' => 'Our *Menus*',
                    )
                )
            ),
        ));
    }

    /**
     * Register PDF Menus Block field group
     */
    private function register_pdf_menus_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_pdf_menus_block',
            'title' => 'PDF Menus Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_pdf_menus_title',
                    'label' => 'Title',
                    'name' => 'title',
                    'type' => 'textarea',
                    'instructions' => 'Enter the title for the menus section. Use * around text to highlight it.',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => 'Our *Menus*',
                    'placeholder' => 'Our *Menus*',
                    'maxlength' => 200,
                    'rows' => 2,
                    'new_lines' => 'br',
                ),
                array(
                    'key' => 'field_pdf_menus_subtitle',
                    'label' => 'Subtitle',
                    'name' => 'subtitle',
                    'type' => 'text',
                    'instructions' => 'Optional subtitle for the menus section',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'default_value' => '',
                    'placeholder' => 'Discover our delicious offerings',
                    'prepend' => '',
                    'append' => '',
                    'maxlength' => 150,
                ),
                array(
                    'key' => 'field_pdf_menus_show_all_filter',
                    'label' => 'Show "All" Filter',
                    'name' => 'show_all_filter',
                    'type' => 'true_false',
                    'instructions' => 'Show an "All" option in the filter to display all menus',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '50',
                        'class' => '',
                        'id' => '',
                    ),
                    'message' => '',
                    'default_value' => 1,
                    'ui' => 1,
                    'ui_on_text' => 'Yes',
                    'ui_off_text' => 'No',
                ),
                array(
                    'key' => 'field_pdf_menus_default_category',
                    'label' => 'Default Category',
                    'name' => 'default_category',
                    'type' => 'select',
                    'instructions' => 'Select which category should be shown by default',
                    'required' => 0,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '50',
                        'class' => '',
                        'id' => '',
                    ),
                    'choices' => array(
                        'all' => 'All',
                        'food' => 'Food',
                        'drinks' => 'Drinks',
                        'wine' => 'Wine',
                        'dessert' => 'Dessert',
                        'special' => 'Special',
                    ),
                    'default_value' => 'all',
                    'allow_null' => 0,
                    'multiple' => 0,
                    'ui' => 1,
                    'ajax' => 0,
                    'return_format' => 'value',
                    'placeholder' => '',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/pdf-menus-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the PDF Menus block',
            'show_in_rest' => 0,
        ));
    }

    /**
     * Register Two Images Block field group
     */
    private function register_two_images_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_two_images_block',
            'title' => 'Two Images Block Settings',
            'fields' => array(
                array(
                    'key' => 'field_two_images_image_1',
                    'label' => 'Image 1',
                    'name' => 'image_1',
                    'type' => 'image',
                    'instructions' => 'Upload the first image',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
                array(
                    'key' => 'field_two_images_image_2',
                    'label' => 'Image 2',
                    'name' => 'image_2',
                    'type' => 'image',
                    'instructions' => 'Upload the second image',
                    'required' => 1,
                    'conditional_logic' => 0,
                    'wrapper' => array(
                        'width' => '',
                        'class' => '',
                        'id' => '',
                    ),
                    'return_format' => 'array',
                    'preview_size' => 'medium',
                    'library' => 'all',
                    'min_width' => '',
                    'min_height' => '',
                    'min_size' => '',
                    'max_width' => '',
                    'max_height' => '',
                    'max_size' => '',
                    'mime_types' => '',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/two-images-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Two Images block',
            'show_in_rest' => 0,
        ));
    }

    /**
     * Register Details Block field group
     */
    private function register_details_block_fields() {
        acf_add_local_field_group(array(
            'key' => 'group_details_block',
            'title' => 'Details Block Settings',
            'fields' => array(
                // Column 1
                array(
                    'key' => 'field_details_col1_title',
                    'label' => 'Column 1 Title',
                    'name' => 'col1_title',
                    'type' => 'text',
                    'instructions' => 'Title for the first column (optional)',
                    'required' => 0,
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                ),
                array(
                    'key' => 'field_details_col1_content',
                    'label' => 'Column 1 Content',
                    'name' => 'col1_content',
                    'type' => 'repeater',
                    'instructions' => 'Add text blocks for column 1',
                    'required' => 0,
                    'sub_fields' => array(
                        array(
                            'key' => 'field_details_col1_text',
                            'label' => 'Text Block',
                            'name' => 'text_block',
                            'type' => 'wysiwyg',
                            'instructions' => 'Add your text content',
                            'required' => 0,
                            'tabs' => 'all',
                            'toolbar' => 'basic',
                            'media_upload' => 0,
                            'delay' => 0,
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                    'min' => 0,
                    'max' => 0,
                    'layout' => 'table',
                    'button_label' => 'Add Text Block',
                ),
                // Column 2
                array(
                    'key' => 'field_details_col2_title',
                    'label' => 'Column 2 Title',
                    'name' => 'col2_title',
                    'type' => 'text',
                    'instructions' => 'Title for the second column (optional)',
                    'required' => 0,
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                ),
                array(
                    'key' => 'field_details_col2_content',
                    'label' => 'Column 2 Content',
                    'name' => 'col2_content',
                    'type' => 'repeater',
                    'instructions' => 'Add text blocks for column 2',
                    'required' => 0,
                    'sub_fields' => array(
                        array(
                            'key' => 'field_details_col2_text',
                            'label' => 'Text Block',
                            'name' => 'text_block',
                            'type' => 'wysiwyg',
                            'instructions' => 'Add your text content',
                            'required' => 0,
                            'tabs' => 'all',
                            'toolbar' => 'basic',
                            'media_upload' => 0,
                            'delay' => 0,
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                    'min' => 0,
                    'max' => 0,
                    'layout' => 'table',
                    'button_label' => 'Add Text Block',
                ),
                // Column 3
                array(
                    'key' => 'field_details_col3_title',
                    'label' => 'Column 3 Title',
                    'name' => 'col3_title',
                    'type' => 'text',
                    'instructions' => 'Title for the third column (optional)',
                    'required' => 0,
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                ),
                array(
                    'key' => 'field_details_col3_content',
                    'label' => 'Column 3 Content',
                    'name' => 'col3_content',
                    'type' => 'repeater',
                    'instructions' => 'Add text blocks for column 3',
                    'required' => 0,
                    'sub_fields' => array(
                        array(
                            'key' => 'field_details_col3_text',
                            'label' => 'Text Block',
                            'name' => 'text_block',
                            'type' => 'wysiwyg',
                            'instructions' => 'Add your text content',
                            'required' => 0,
                            'tabs' => 'all',
                            'toolbar' => 'basic',
                            'media_upload' => 0,
                            'delay' => 0,
                        ),
                    ),
                    'wrapper' => array(
                        'width' => '33.33',
                        'class' => '',
                        'id' => '',
                    ),
                    'min' => 0,
                    'max' => 0,
                    'layout' => 'table',
                    'button_label' => 'Add Text Block',
                ),
            ),
            'location' => array(
                array(
                    array(
                        'param' => 'block',
                        'operator' => '==',
                        'value' => 'acf/details-block',
                    ),
                ),
            ),
            'menu_order' => 0,
            'position' => 'normal',
            'style' => 'default',
            'label_placement' => 'top',
            'instruction_placement' => 'label',
            'hide_on_screen' => '',
            'active' => true,
            'description' => 'Settings for the Details block with 3 columns',
            'show_in_rest' => 0,
        ));
    }
}

// Initialize the ACF Blocks Manager
new ACF_Blocks_Manager();
