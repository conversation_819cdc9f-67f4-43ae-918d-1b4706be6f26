# Centralized ACF Management System

This document describes the new centralized ACF (Advanced Custom Fields) management system that has been implemented for this WordPress theme.

## Overview

All ACF blocks, fields, and post types are now managed from a single file (`acf.php`) for better organization, maintainability, and code management.

## File Structure

```
/
├── acf.php                     # Main ACF management file
├── functions.php               # Updated to use centralized system
├── blocks/
│   ├── rooms-block.php         # New Rooms block template
│   └── css/
│       └── rooms-block.css     # Rooms block styles
├── single-rooms.php            # Single room template
├── archive-rooms.php           # Rooms archive template
└── includes/                   # Legacy files (still used for some features)
    ├── whatsapp-settings.php
    └── add-gallery-field.php
```

## New Features

### 1. Rooms Post Type

A new custom post type "Rooms" has been created with the following features:

#### Fields:
- **Room Color**: Color picker for theme customization
- **Room Title**: Main title for the room
- **Room Description**: Rich text description
- **Room Gallery**: Image gallery for multiple photos
- **Room Amenities**: Repeater field with name and icon
- **Room Price**: Price per night
- **Room Capacity**: Maximum number of guests
- **Room Size**: Room size in square meters

#### Templates:
- `single-rooms.php`: Individual room display with gallery slider
- `archive-rooms.php`: Rooms listing page with grid layout

### 2. Rooms Block

A new Gutenberg block for displaying rooms with the following options:

#### Block Settings:
- **Display Type**: Grid or List layout
- **Columns**: 1-4 columns for grid layout
- **Rooms to Show**: All rooms or selected rooms
- **Selected Rooms**: Choose specific rooms to display
- **Show Price**: Toggle price display
- **Show Capacity**: Toggle capacity display
- **Show Amenities**: Toggle amenities display

## ACF Manager Class

The `ACF_Manager` class in `acf.php` handles:

1. **Post Type Registration**
   - Rooms post type
   - Popup post type (migrated from old system)

2. **Block Registration**
   - New Rooms block
   - All existing blocks (migrated from functions.php)

3. **Field Group Registration**
   - Rooms fields
   - Rooms block settings
   - Popup fields (migrated)

4. **Options Pages**
   - Theme settings page

## Usage

### Adding New Blocks

To add a new block, add it to the `register_existing_blocks()` method in `acf.php`:

```php
array(
    'name' => 'your_block_name',
    'title' => 'Your Block Title',
    'template' => 'blocks/your-block.php',
    'category' => 'your-category',
    'keywords' => array('keyword1', 'keyword2'),
),
```

### Adding New Post Types

Add new post types in the `register_post_types()` method and create a corresponding registration method:

```php
public function register_post_types() {
    $this->register_rooms_post_type();
    $this->register_your_new_post_type(); // Add this line
    $this->register_popup_post_type();
}
```

### Adding New Field Groups

Add field groups in the `register_field_groups()` method and create a corresponding registration method:

```php
public function register_field_groups() {
    $this->register_rooms_fields();
    $this->register_your_new_fields(); // Add this line
    $this->register_popup_fields();
}
```

## Styling

### Rooms Block Styling

The Rooms block includes comprehensive CSS in `blocks/css/rooms-block.css` with:
- Responsive grid layouts
- Hover effects and animations
- Color theming using CSS custom properties
- Mobile-optimized design

### Room Templates Styling

Both single and archive room templates include inline styles for:
- Gallery sliders with thumbnails
- Responsive layouts
- Interactive elements
- Consistent design system

## Migration Notes

### What Was Changed:
1. Moved all block registrations from `functions.php` to `acf.php`
2. Moved post type registrations from `includes/custom-post-types.php` to `acf.php`
3. Moved field registrations from `includes/acf-fields.php` to `acf.php`
4. Updated `functions.php` to include only the centralized `acf.php` file

### What Was Preserved:
1. All existing blocks continue to work
2. All existing functionality is maintained
3. Backward compatibility is preserved
4. Helper functions like `render_button()` are kept

## Benefits

1. **Centralized Management**: All ACF-related code in one place
2. **Better Organization**: Clear structure and separation of concerns
3. **Easier Maintenance**: Single file to manage all ACF configurations
4. **Improved Performance**: Reduced file includes and better code organization
5. **Enhanced Scalability**: Easy to add new blocks, fields, and post types

## English Language Implementation

All new features are implemented in English as requested:
- Field labels and descriptions
- Block titles and categories
- Template text and labels
- Comments and documentation

## Next Steps

1. Test the new Rooms functionality in WordPress admin
2. Create some sample room posts to verify the system
3. Test the Rooms block in the Gutenberg editor
4. Verify all existing blocks still work correctly
5. Add any additional customizations as needed

The system is now ready for use and provides a solid foundation for future ACF-related development.
