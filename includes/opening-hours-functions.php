<?php
/**
 * Opening Hours Functions
 * 
 * Functions for managing and displaying restaurant opening hours
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get opening hours for a specific day
 * 
 * @param string $day Day of the week (monday, tuesday, etc.)
 * @return array Array of opening hours for the day
 */
function get_opening_hours_for_day($day) {
    $field_name = 'opening_hours_' . strtolower($day);
    $hours = get_field($field_name, 'option');
    
    if (!$hours || !is_array($hours)) {
        return array();
    }
    
    return $hours;
}

/**
 * Check if restaurant is currently open
 * 
 * @return array Array with 'is_open' boolean and 'current_hours' array
 */
function is_restaurant_open() {
    // Set timezone to UK time
    $timezone = new DateTimeZone('Europe/London');
    $now = new DateTime('now', $timezone);
    
    $current_day = strtolower($now->format('l')); // monday, tuesday, etc.
    $current_time = $now->format('H:i');
    
    $hours = get_opening_hours_for_day($current_day);
    
    if (empty($hours)) {
        return array(
            'is_open' => false,
            'current_hours' => array(),
            'day' => $current_day,
            'time' => $current_time
        );
    }
    
    foreach ($hours as $time_slot) {
        $open_time = $time_slot['open_time'];
        $close_time = $time_slot['close_time'];
        
        if ($current_time >= $open_time && $current_time < $close_time) {
            return array(
                'is_open' => true,
                'current_hours' => $hours,
                'day' => $current_day,
                'time' => $current_time,
                'current_slot' => $time_slot
            );
        }
    }
    
    return array(
        'is_open' => false,
        'current_hours' => $hours,
        'day' => $current_day,
        'time' => $current_time
    );
}

/**
 * Format time for display
 * 
 * @param string $time Time in H:i format
 * @return string Formatted time
 */
function format_display_time($time) {
    $datetime = DateTime::createFromFormat('H:i', $time);
    if (!$datetime) {
        return $time;
    }
    return $datetime->format('g:ia');
}

/**
 * Get formatted opening hours for display
 * 
 * @param string $day Day of the week
 * @return string Formatted opening hours string
 */
function get_formatted_opening_hours($day = null) {
    if (!$day) {
        $timezone = new DateTimeZone('Europe/London');
        $now = new DateTime('now', $timezone);
        $day = strtolower($now->format('l'));
    }
    
    $hours = get_opening_hours_for_day($day);
    
    if (empty($hours)) {
        return 'CLOSED';
    }
    
    $formatted_hours = array();
    foreach ($hours as $time_slot) {
        $open = format_display_time($time_slot['open_time']);
        $close = format_display_time($time_slot['close_time']);
        $formatted_hours[] = $open . ' – ' . $close;
    }
    
    return implode(' + ', $formatted_hours);
}

/**
 * Display opening hours status in header
 *
 * @return string HTML for opening hours display
 */
function display_header_opening_hours() {
    $status = is_restaurant_open();
    $formatted_hours = get_formatted_opening_hours();

    // If no hours are set, use default hours but check current time
    if (empty($status['current_hours']) && $formatted_hours === 'CLOSED') {
        $formatted_hours = '9:30AM – 3:30PM + 5:00PM – 10:00PM';

        // Check if current time falls within default hours
        $timezone = new DateTimeZone('Europe/London');
        $now = new DateTime('now', $timezone);
        $current_time = $now->format('H:i');

        $is_open_now = ($current_time >= '09:30' && $current_time < '15:30') ||
                       ($current_time >= '17:00' && $current_time < '22:00');

        $status_text = $is_open_now ? 'OPEN' : 'CLOSED';
        $status_class = $is_open_now ? 'open' : 'closed';
        $status_color = $is_open_now ? '#0DA766' : '#E55D2D';
    } else {
        $status_class = $status['is_open'] ? 'open' : 'closed';
        $status_text = $status['is_open'] ? 'OPEN' : 'CLOSED';
        $status_color = $status['is_open'] ? '#0DA766' : '#E55D2D'; // green or primary color
    }

    $html = '<div class="opening-hours-status ' . $status_class . '">';
    $html .= '<span class="status-indicator" style="background-color: ' . $status_color . ';"></span>';
    $html .= '<span class="status-text">' . $status_text . '</span>';
    $html .= '<span class="status-separator"> | </span>';
    $html .= '<span class="opening-hours">' . $formatted_hours . '</span>';
    $html .= '</div>';

    return $html;
}

/**
 * Get all opening hours for all days
 *
 * @return array Array of all opening hours
 */
function get_all_opening_hours() {
    $days = array('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday');
    $all_hours = array();

    foreach ($days as $day) {
        $all_hours[$day] = get_opening_hours_for_day($day);
    }

    return $all_hours;
}

/**
 * Set default opening hours (for testing/initial setup)
 * This function can be called once to set up default hours
 */
function set_default_opening_hours() {
    $default_hours = array(
        array(
            'open_time' => '09:30',
            'close_time' => '15:30'
        ),
        array(
            'open_time' => '17:00',
            'close_time' => '22:00'
        )
    );

    $days = array('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday');

    foreach ($days as $day) {
        $field_name = 'opening_hours_' . $day;
        update_field($field_name, $default_hours, 'option');
    }
}

/**
 * Debug function to show current status
 */
function debug_opening_hours() {
    if (!current_user_can('administrator')) {
        return;
    }

    $status = is_restaurant_open();
    echo '<div style="position: fixed; top: 100px; right: 20px; background: white; padding: 20px; border: 1px solid #ccc; z-index: 9999;">';
    echo '<h4>Opening Hours Debug</h4>';
    echo '<p><strong>Current Day:</strong> ' . $status['day'] . '</p>';
    echo '<p><strong>Current Time:</strong> ' . $status['time'] . '</p>';
    echo '<p><strong>Is Open:</strong> ' . ($status['is_open'] ? 'YES' : 'NO') . '</p>';
    echo '<p><strong>Today\'s Hours:</strong></p>';
    if (!empty($status['current_hours'])) {
        foreach ($status['current_hours'] as $slot) {
            echo '<p>' . format_display_time($slot['open_time']) . ' - ' . format_display_time($slot['close_time']) . '</p>';
        }
    } else {
        echo '<p>CLOSED</p>';
    }
    echo '</div>';
}
