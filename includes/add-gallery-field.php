<?php
/**
 * Add Gallery Field to Sticky Date Text Block
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Voeg een hook toe om de ACF-velden aan te passen
add_action('acf/init', 'add_gallery_field_to_sticky_date_text_block');

function add_gallery_field_to_sticky_date_text_block() {
    // Controleer of ACF is geactiveerd
    if (!function_exists('acf_add_local_field')) {
        return;
    }
    
    // Voeg het gallery veld toe aan de items repeater
    acf_add_local_field(array(
        'key' => 'field_sticky_date_text_images',
        'label' => 'Images',
        'name' => 'images',
        'type' => 'gallery',
        'instructions' => 'Upload multiple images for the slideshow. If you add multiple images, they will automatically rotate.',
        'required' => 0,
        'conditional_logic' => 0,
        'wrapper' => array(
            'width' => '',
            'class' => '',
            'id' => '',
        ),
        'return_format' => 'array',
        'preview_size' => 'medium',
        'insert' => 'append',
        'library' => 'all',
        'min' => 0,
        'max' => '',
        'min_width' => '',
        'min_height' => '',
        'min_size' => '',
        'max_width' => '',
        'max_height' => '',
        'max_size' => '',
        'mime_types' => '',
        'parent' => 'field_items', // Dit is de key van de items repeater
    ));
    
    // Voeg het slideshow interval veld toe aan de items repeater
    acf_add_local_field(array(
        'key' => 'field_sticky_date_text_slideshow_interval',
        'label' => 'Slideshow Interval',
        'name' => 'slideshow_interval',
        'type' => 'number',
        'instructions' => 'Time in seconds between image changes',
        'required' => 0,
        'conditional_logic' => 0,
        'wrapper' => array(
            'width' => '',
            'class' => '',
            'id' => '',
        ),
        'default_value' => 3,
        'placeholder' => '',
        'prepend' => '',
        'append' => 'seconds',
        'min' => 1,
        'max' => 10,
        'step' => 1,
        'parent' => 'field_items', // Dit is de key van de items repeater
    ));
}
