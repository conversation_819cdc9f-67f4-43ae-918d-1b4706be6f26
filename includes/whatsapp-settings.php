<?php
/**
 * Register WhatsApp Button Settings
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

// Add WhatsApp Settings page
if (function_exists('acf_add_options_page')) {
    acf_add_options_page(array(
        'page_title'    => 'WhatsApp Button',
        'menu_title'    => 'WhatsApp Button',
        'menu_slug'     => 'whatsapp-button-settings',
        'capability'    => 'edit_posts',
        'icon_url'      => 'dashicons-phone', // Using a phone icon
        'position'      => 30,
        'redirect'      => false
    ));
}

// Add WhatsApp Button Fields
if (function_exists('acf_add_local_field_group')) {
    acf_add_local_field_group(array(
        'key' => 'group_whatsapp_button',
        'title' => 'WhatsApp Button Settings',
        'fields' => array(
            array(
                'key' => 'field_whatsapp_enable',
                'label' => 'Enable WhatsApp Button',
                'name' => 'whatsapp_enable',
                'type' => 'true_false',
                'instructions' => 'Enable or disable the WhatsApp button',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'message' => '',
                'default_value' => 1,
                'ui' => 1,
                'ui_on_text' => 'Enabled',
                'ui_off_text' => 'Disabled',
            ),
            array(
                'key' => 'field_whatsapp_number',
                'label' => 'WhatsApp Number',
                'name' => 'whatsapp_number',
                'type' => 'text',
                'instructions' => 'Enter the WhatsApp number in international format without spaces or special characters (e.g., 31612345678)',
                'required' => 1,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_whatsapp_enable',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => '31612345678',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_whatsapp_profile_image',
                'label' => 'Profile Image',
                'name' => 'whatsapp_profile_image',
                'type' => 'image',
                'instructions' => 'Upload a profile image for the WhatsApp button (preferably square)',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_whatsapp_enable',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'min_width' => '',
                'min_height' => '',
                'min_size' => '',
                'max_width' => '',
                'max_height' => '',
                'max_size' => '',
                'mime_types' => 'jpg,jpeg,png',
            ),
            array(
                'key' => 'field_whatsapp_available',
                'label' => 'Available Now',
                'name' => 'whatsapp_available',
                'type' => 'true_false',
                'instructions' => 'Show the availability indicator',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_whatsapp_enable',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'message' => '',
                'default_value' => 1,
                'ui' => 1,
                'ui_on_text' => 'Available',
                'ui_off_text' => 'Unavailable',
            ),
            array(
                'key' => 'field_whatsapp_message',
                'label' => 'Pre-filled Message',
                'name' => 'whatsapp_message',
                'type' => 'textarea',
                'instructions' => 'Enter the message that will be pre-filled when someone clicks the WhatsApp button',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_whatsapp_enable',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => 'Beste Allemans, ik zou graag willen reserveren voor …',
                'placeholder' => '',
                'maxlength' => '',
                'rows' => 4,
                'new_lines' => 'wpautop',
            ),
            array(
                'key' => 'field_whatsapp_show_tooltip',
                'label' => 'Show Tooltip',
                'name' => 'whatsapp_show_tooltip',
                'type' => 'true_false',
                'instructions' => 'Show a tooltip message above the WhatsApp button',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_whatsapp_enable',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'message' => '',
                'default_value' => 0,
                'ui' => 1,
                'ui_on_text' => 'Show',
                'ui_off_text' => 'Hide',
            ),
            array(
                'key' => 'field_whatsapp_tooltip_text',
                'label' => 'Tooltip Text',
                'name' => 'whatsapp_tooltip_text',
                'type' => 'text',
                'instructions' => 'Enter the text to display in the tooltip',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_whatsapp_enable',
                            'operator' => '==',
                            'value' => '1',
                        ),
                        array(
                            'field' => 'field_whatsapp_show_tooltip',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => 'Kan ik u helpen?',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'options_page',
                    'operator' => '==',
                    'value' => 'whatsapp-button-settings',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));
}
