<?php
/**
 * Register ACF Fields for Popups
 */

if (function_exists('acf_add_local_field_group')) {
    // Check if options page exists, if not create it
    if (function_exists('acf_add_options_page')) {
        acf_add_options_page(array(
            'page_title' => 'Theme General Settings',
            'menu_title' => 'Theme Settings',
            'menu_slug' => 'theme-general-settings',
            'capability' => 'edit_posts',
            'redirect' => false
        ));
    }
    acf_add_local_field_group(array(
        'key' => 'group_popup_settings',
        'title' => 'Popup Settings',
        'fields' => array(
            array(
                'key' => 'field_popup_enabled',
                'label' => 'Enable Popup',
                'name' => 'popup_enabled',
                'type' => 'true_false',
                'instructions' => 'Enable or disable this popup',
                'required' => 0,
                'conditional_logic' => 0,
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'message' => '',
                'default_value' => 0,
                'ui' => 1,
                'ui_on_text' => 'Enabled',
                'ui_off_text' => 'Disabled',
            ),
            array(
                'key' => 'field_popup_media_type',
                'label' => 'Media Type',
                'name' => 'popup_media_type',
                'type' => 'radio',
                'instructions' => 'Select the type of media to display in the popup',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_popup_enabled',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'image' => 'Image',
                    'video' => 'Video',
                    'none' => 'No Media',
                ),
                'allow_null' => 0,
                'other_choice' => 0,
                'default_value' => 'image',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'save_other_choice' => 0,
            ),
            array(
                'key' => 'field_popup_image',
                'label' => 'Popup Image',
                'name' => 'popup_image',
                'type' => 'image',
                'instructions' => 'Upload an image for the popup',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_popup_media_type',
                            'operator' => '==',
                            'value' => 'image',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'return_format' => 'array',
                'preview_size' => 'medium',
                'library' => 'all',
                'min_width' => '',
                'min_height' => '',
                'min_size' => '',
                'max_width' => '',
                'max_height' => '',
                'max_size' => '',
                'mime_types' => '',
            ),
            array(
                'key' => 'field_popup_video_type',
                'label' => 'Video Type',
                'name' => 'popup_video_type',
                'type' => 'radio',
                'instructions' => 'Select the type of video source',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_popup_media_type',
                            'operator' => '==',
                            'value' => 'video',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'choices' => array(
                    'file' => 'Upload File',
                    'url' => 'External URL',
                ),
                'allow_null' => 0,
                'other_choice' => 0,
                'default_value' => 'file',
                'layout' => 'horizontal',
                'return_format' => 'value',
                'save_other_choice' => 0,
            ),
            array(
                'key' => 'field_popup_video',
                'label' => 'Popup Video File',
                'name' => 'popup_video',
                'type' => 'file',
                'instructions' => 'Upload a video file for the popup',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_popup_media_type',
                            'operator' => '==',
                            'value' => 'video',
                        ),
                        array(
                            'field' => 'field_popup_video_type',
                            'operator' => '==',
                            'value' => 'file',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'return_format' => 'array',
                'library' => 'all',
                'min_size' => '',
                'max_size' => '',
                'mime_types' => 'mp4,webm',
            ),
            array(
                'key' => 'field_popup_video_url',
                'label' => 'Popup Video URL',
                'name' => 'popup_video_url',
                'type' => 'url',
                'instructions' => 'Enter the URL for an external video (YouTube, Vimeo, or direct MP4 link)',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_popup_media_type',
                            'operator' => '==',
                            'value' => 'video',
                        ),
                        array(
                            'field' => 'field_popup_video_type',
                            'operator' => '==',
                            'value' => 'url',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => 'https://',
            ),
            array(
                'key' => 'field_popup_title',
                'label' => 'Popup Title',
                'name' => 'popup_title',
                'type' => 'text',
                'instructions' => 'Enter the title for the popup',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_popup_enabled',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'placeholder' => '',
                'prepend' => '',
                'append' => '',
                'maxlength' => '',
            ),
            array(
                'key' => 'field_popup_text',
                'label' => 'Popup Text',
                'name' => 'popup_text',
                'type' => 'wysiwyg',
                'instructions' => 'Enter the text content for the popup',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_popup_enabled',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => '',
                'tabs' => 'all',
                'toolbar' => 'full',
                'media_upload' => 1,
                'delay' => 0,
            ),
            array(
                'key' => 'field_popup_buttons',
                'label' => 'Popup Buttons',
                'name' => 'popup_buttons',
                'type' => 'repeater',
                'instructions' => 'Add buttons to the popup',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_popup_enabled',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'collapsed' => '',
                'min' => 0,
                'max' => 2,
                'layout' => 'table',
                'button_label' => 'Add Button',
                'sub_fields' => array(
                    array(
                        'key' => 'field_popup_button_text',
                        'label' => 'Button Text',
                        'name' => 'button_text',
                        'type' => 'text',
                        'instructions' => '',
                        'required' => 1,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'default_value' => '',
                        'placeholder' => '',
                        'prepend' => '',
                        'append' => '',
                        'maxlength' => '',
                    ),
                    array(
                        'key' => 'field_popup_button_url',
                        'label' => 'Button URL',
                        'name' => 'button_url',
                        'type' => 'url',
                        'instructions' => '',
                        'required' => 1,
                        'conditional_logic' => 0,
                        'wrapper' => array(
                            'width' => '',
                            'class' => '',
                            'id' => '',
                        ),
                        'default_value' => '',
                        'placeholder' => '',
                    ),
                ),
            ),
            array(
                'key' => 'field_popup_expiry_days',
                'label' => 'Expiry Days',
                'name' => 'popup_expiry_days',
                'type' => 'number',
                'instructions' => 'Number of days before showing the popup again after it has been closed (default: 2)',
                'required' => 0,
                'conditional_logic' => array(
                    array(
                        array(
                            'field' => 'field_popup_enabled',
                            'operator' => '==',
                            'value' => '1',
                        ),
                    ),
                ),
                'wrapper' => array(
                    'width' => '',
                    'class' => '',
                    'id' => '',
                ),
                'default_value' => 2,
                'placeholder' => '',
                'prepend' => '',
                'append' => 'days',
                'min' => 0,
                'max' => 365,
                'step' => 1,
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'post_type',
                    'operator' => '==',
                    'value' => 'popup',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
    ));
}
