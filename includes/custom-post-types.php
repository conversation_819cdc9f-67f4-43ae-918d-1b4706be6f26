<?php
/**
 * Register Custom Post Types
 */

// Register Popup Custom Post Type
function register_popup_post_type() {
    $labels = array(
        'name'                  => _x('Popups', 'Post Type General Name', 'allemans'),
        'singular_name'         => _x('Popup', 'Post Type Singular Name', 'allemans'),
        'menu_name'             => __('Popups', 'allemans'),
        'name_admin_bar'        => __('Popup', 'allemans'),
        'archives'              => __('Popup Archives', 'allemans'),
        'attributes'            => __('Popup Attributes', 'allemans'),
        'parent_item_colon'     => __('Parent Popup:', 'allemans'),
        'all_items'             => __('All Popups', 'allemans'),
        'add_new_item'          => __('Add New Popup', 'allemans'),
        'add_new'               => __('Add New', 'allemans'),
        'new_item'              => __('New Popup', 'allemans'),
        'edit_item'             => __('Edit Popup', 'allemans'),
        'update_item'           => __('Update Popup', 'allemans'),
        'view_item'             => __('View Popup', 'allemans'),
        'view_items'            => __('View Popups', 'allemans'),
        'search_items'          => __('Search Popup', 'allemans'),
        'not_found'             => __('Not found', 'allemans'),
        'not_found_in_trash'    => __('Not found in Trash', 'allemans'),
        'featured_image'        => __('Featured Image', 'allemans'),
        'set_featured_image'    => __('Set featured image', 'allemans'),
        'remove_featured_image' => __('Remove featured image', 'allemans'),
        'use_featured_image'    => __('Use as featured image', 'allemans'),
        'insert_into_item'      => __('Insert into popup', 'allemans'),
        'uploaded_to_this_item' => __('Uploaded to this popup', 'allemans'),
        'items_list'            => __('Popups list', 'allemans'),
        'items_list_navigation' => __('Popups list navigation', 'allemans'),
        'filter_items_list'     => __('Filter popups list', 'allemans'),
    );
    
    $args = array(
        'label'                 => __('Popup', 'allemans'),
        'description'           => __('Popup items', 'allemans'),
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 20,
        'menu_icon'             => 'dashicons-megaphone',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => false,
        'can_export'            => true,
        'has_archive'           => false,
        'exclude_from_search'   => true,
        'publicly_queryable'    => false,
        'capability_type'       => 'page',
        'show_in_rest'          => true,
    );
    
    register_post_type('popup', $args);
}
add_action('init', 'register_popup_post_type', 0);
